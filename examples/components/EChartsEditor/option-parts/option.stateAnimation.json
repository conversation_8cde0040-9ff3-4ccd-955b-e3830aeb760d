{"duration": {"desc": "\n\n<p>状态切换的动画时长，设为 0 则关闭状态动画。</p>\n", "uiControl": {"type": "number", "min": "0", "default": "300", "step": "20"}}, "easing": {"desc": "\n\n<p>状态切换的动画缓动。</p>\n", "uiControl": {"type": "enum", "options": "linear,quadraticIn,quadraticOut,quadraticInOut,cubicIn,cubicOut,cubicInOut,quarticIn,quarticOut,quarticInOut,quinticIn,quinticOut,quinticInOut,sinusoidalIn,sinusoidalOut,sinusoidalInOut,exponentialIn,exponentialOut,exponentialInOut,circularIn,circularOut,circularInOut,elasticIn,elasticOut,elasticInOut,backIn,backOut,backInOut,bounceIn,bounceOut,bounceInOut"}}}