{"id": {"desc": "<p>组件 ID。默认不指定。指定则可用于在 option 或者 API 中引用组件。</p>\n"}, "coordinateSystem": {"desc": "<p>该系列使用的坐标系，可选：</p>\n<ul>\n<li><p><code class=\"codespan\">&#39;parallel&#39;</code></p>\n<p>  使用平行坐标系，通过 <a href=\"#series-parallel.parallelIndex\">parallelIndex</a> 指定相应的平行坐标系组件。</p>\n</li>\n</ul>\n"}, "parallelIndex": {"desc": "<p>使用的<a href=\"#parallel\">平行坐标系</a>的 index，在单个图表实例中存在多个平行坐标系的时候有用。</p>\n"}, "name": {"desc": "<p>系列名称，用于<a href=\"#tooltip\">tooltip</a>的显示，<a href=\"#legend\">legend</a> 的图例筛选，在 <code class=\"codespan\">setOption</code> 更新数据和配置项时用于指定对应的系列。</p>\n"}, "lineStyle": {"desc": "<p>线条样式。</p>\n"}, "lineStyle.color": {"desc": "\n\n<p>线的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "lineStyle.width": {"desc": "\n\n<p>线宽。</p>\n", "uiControl": {"type": "number", "value": "2", "min": "0", "step": "0.5"}}, "lineStyle.type": {"desc": "\n\n<p>线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "lineStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "lineStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "lineStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "lineStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "lineStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "0.45", "min": "0", "max": "1", "step": "0.01"}}, "emphasis.lineStyle.color": {"desc": "\n\n<p>线的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "emphasis.lineStyle.width": {"desc": "\n\n<p>线宽。</p>\n", "uiControl": {"type": "number", "value": "2", "min": "0", "step": "0.5"}}, "emphasis.lineStyle.type": {"desc": "\n\n<p>线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "emphasis.lineStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "emphasis.lineStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "emphasis.lineStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "emphasis.lineStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "emphasis.lineStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "0.45", "min": "0", "max": "1", "step": "0.01"}}, "inactiveOpacity": {"desc": "\n\n<p>框选时，未被选中的条线会设置成这个『透明度』（从而可以达到变暗的效果）。</p>\n", "uiControl": {"type": "number", "min": "0", "max": "1", "default": "0.05", "step": "0.01"}}, "activeOpacity": {"desc": "\n\n<p>框选时，选中的条线会设置成这个『透明度』（从而可以达到高亮的效果）。</p>\n", "uiControl": {"type": "number", "min": "0", "max": "1", "default": "1", "step": "0.01"}}, "realtime": {"desc": "\n\n<p>是否实时刷新。</p>\n", "uiControl": {"type": "boolean", "default": "true"}}, "smooth": {"desc": "\n\n<p>是否使用平滑曲线。默认为 <code class=\"codespan\">false</code>，可以设置为 <code class=\"codespan\">true</code> 或者一个范围为 <code class=\"codespan\">0</code> 到 <code class=\"codespan\">1</code> 的小数值，指定平滑程度。</p>\n", "uiControl": {"type": "boolean"}}, "progressive": {"desc": "<p>渐进式渲染时每一帧绘制图形数量，设为 0 时不启用渐进式渲染，支持每个系列单独配置。</p>\n<p>在图中有数千到几千万图形元素的时候，一下子把图形绘制出来，或者交互重绘的时候可能会造成界面的卡顿甚至假死。ECharts 4 开始全流程支持渐进渲染（progressive rendering），渲染的时候会把创建好的图形分到数帧中渲染，每一帧渲染只渲染指定数量的图形。</p>\n<p>该配置项就是用于配置该系列每一帧渲染的图形数，可以根据图表图形复杂度的需要适当调整这个数字使得在不影响交互流畅性的前提下达到绘制速度的最大化。比如在 lines 图或者平行坐标中线宽大于 1 的 polyline 绘制会很慢，这个数字就可以设置小一点，而线宽小于等于 1 的 polyline 绘制非常快，该配置项就可以相对调得比较大。</p>\n"}, "progressiveThreshold": {"desc": "<p>启用渐进式渲染的图形数量阈值，在单个系列的图形数量超过该阈值时启用渐进式渲染。</p>\n"}, "progressiveChunkMode": {"desc": "<p>分片的方式。可选值：</p>\n<ul>\n<li><code class=\"codespan\">&#39;sequential&#39;</code>: 按照数据的顺序分片。缺点是渲染过程不自然。</li>\n<li><code class=\"codespan\">&#39;mod&#39;</code>: 取模分片，即每个片段中的点会遍布于整个数据，从而能够视觉上均匀得渲染。</li>\n</ul>\n"}, "data": {"desc": "<p>例如 <a href=\"#series-parallel.data\">series-parallel.data</a> 中有如下数据：</p>\n<pre><code class=\"lang-javascript\">[\n    [1,  55,  9,   56,  0.46,  18,  6,  &#39;良&#39;],\n    [2,  25,  11,  21,  0.65,  34,  9,  &#39;优&#39;],\n    [3,  56,  7,   63,  0.3,   14,  5,  &#39;良&#39;],\n    [4,  33,  7,   29,  0.33,  16,  6,  &#39;优&#39;],\n    { // 数据项也可以是 Object，从而里面能含有对线条的特殊设置。\n        value: [5,  42,  24,  44,  0.76,  40,  16, &#39;优&#39;]\n        lineStyle: {...},\n    }\n    ...\n]\n</code></pre>\n<p>数据中，每一行是一个『数据项』，每一列属于一个『维度』。（例如上面数据每一列的含义分别是：『日期』,『AQI指数』, 『PM2.5』, 『PM10』, 『一氧化碳值』, 『二氧化氮值』, 『二氧化硫值』）。</p>\n"}, "data.name": {"desc": "<p>数据项名称。</p>\n"}, "data.value": {"desc": "<p>数据项值。</p>\n"}, "data.lineStyle": {"desc": "<p>线条样式。</p>\n"}, "data.color": {"desc": "\n\n<p>线的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "data.width": {"desc": "\n\n<p>线宽。</p>\n", "uiControl": {"type": "number", "value": "2", "min": "0", "step": "0.5"}}, "data.type": {"desc": "\n\n<p>线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "data.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "data.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "data.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "data.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "data.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "0.45", "min": "0", "max": "1", "step": "0.01"}}, "data.emphasis.lineStyle.color": {"desc": "\n\n<p>线的颜色。</p>\n<blockquote>\n<p>颜色可以使用 RGB 表示，比如 <code class=\"codespan\">&#39;rgb(128, 128, 128)&#39;</code>，如果想要加上 alpha 通道表示不透明度，可以使用 RGBA，比如 <code class=\"codespan\">&#39;rgba(128, 128, 128, 0.5)&#39;</code>，也可以使用十六进制格式，比如 <code class=\"codespan\">&#39;#ccc&#39;</code>。除了纯色之外颜色也支持渐变色和纹理填充</p>\n<pre><code class=\"lang-js\">// 线性渐变，前四个参数分别是 x0, y0, x2, y2, 范围从 0 - 1，相当于在图形包围盒中的百分比，如果 globalCoord 为 `true`，则该四个值是绝对的像素位置\ncolor: {\n    type: &#39;linear&#39;,\n    x: 0,\n    y: 0,\n    x2: 0,\n    y2: 1,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变\ncolor: {\n    type: &#39;radial&#39;,\n    x: 0.5,\n    y: 0.5,\n    r: 0.5,\n    colorStops: [{\n        offset: 0, color: &#39;red&#39; // 0% 处的颜色\n    }, {\n        offset: 1, color: &#39;blue&#39; // 100% 处的颜色\n    }],\n    global: false // 缺省为 false\n}\n// 纹理填充\ncolor: {\n    image: imageDom, // 支持为 HTMLImageElement, HTMLCanvasElement，不支持路径字符串\n    repeat: &#39;repeat&#39; // 是否平铺，可以是 &#39;repeat-x&#39;, &#39;repeat-y&#39;, &#39;no-repeat&#39;\n}\n</code></pre>\n</blockquote>\n", "uiControl": {"type": "color"}}, "data.emphasis.lineStyle.width": {"desc": "\n\n<p>线宽。</p>\n", "uiControl": {"type": "number", "value": "2", "min": "0", "step": "0.5"}}, "data.emphasis.lineStyle.type": {"desc": "\n\n<p>线的类型。</p>\n<p>可选：</p>\n<ul>\n<li><code class=\"codespan\">&#39;solid&#39;</code></li>\n<li><code class=\"codespan\">&#39;dashed&#39;</code></li>\n<li><code class=\"codespan\">&#39;dotted&#39;</code></li>\n</ul>\n", "uiControl": {"type": "enum", "default": "solid", "options": "solid,dashed,dotted"}}, "data.emphasis.lineStyle.shadowBlur": {"desc": "\n\n<p>图形阴影的模糊大小。该属性配合 <code class=\"codespan\">shadowColor</code>,<code class=\"codespan\">shadowOffsetX</code>, <code class=\"codespan\">shadowOffsetY</code> 一起设置图形的阴影效果。</p>\n<p>示例：</p>\n<pre><code class=\"lang-js\">{\n    shadowColor: &#39;rgba(0, 0, 0, 0.5)&#39;,\n    shadowBlur: 10\n}\n</code></pre>\n", "uiControl": {"type": "number", "default": "", "min": "0", "step": "0.5"}}, "data.emphasis.lineStyle.shadowColor": {"desc": "\n\n<p>阴影颜色。支持的格式同<code class=\"codespan\">color</code>。</p>\n", "uiControl": {"type": "color", "default": ""}}, "data.emphasis.lineStyle.shadowOffsetX": {"desc": "\n\n<p>阴影水平方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "data.emphasis.lineStyle.shadowOffsetY": {"desc": "\n\n<p>阴影垂直方向上的偏移距离。</p>\n", "uiControl": {"type": "number", "default": "0", "step": "0.5"}}, "data.emphasis.lineStyle.opacity": {"desc": "\n\n<p>图形透明度。支持从 0 到 1 的数字，为 0 时不绘制该图形。</p>\n", "uiControl": {"type": "number", "default": "0.45", "min": "0", "max": "1", "step": "0.01"}}, "zlevel": {"desc": "<p>平行坐标所有图形的 zlevel 值。</p>\n<p><code class=\"codespan\">zlevel</code>用于 Canvas 分层，不同<code class=\"codespan\">zlevel</code>值的图形会放置在不同的 Canvas 中，Canvas 分层是一种常见的优化手段。我们可以把一些图形变化频繁（例如有动画）的组件设置成一个单独的<code class=\"codespan\">zlevel</code>。需要注意的是过多的 Canvas 会引起内存开销的增大，在手机端上需要谨慎使用以防崩溃。</p>\n<p><code class=\"codespan\">zlevel</code> 大的 Canvas 会放在 <code class=\"codespan\">zlevel</code> 小的 Canvas 的上面。</p>\n"}, "z": {"desc": "<p>平行坐标组件的所有图形的<code class=\"codespan\">z</code>值。控制图形的前后顺序。<code class=\"codespan\">z</code>值小的图形会被<code class=\"codespan\">z</code>值大的图形覆盖。</p>\n<p><code class=\"codespan\">z</code>相比<code class=\"codespan\">zlevel</code>优先级更低，而且不会创建新的 Canvas。</p>\n"}, "silent": {"desc": "\n\n<p>图形是否不响应和触发鼠标事件，默认为 false，即响应和触发鼠标事件。</p>\n", "uiControl": {"type": "boolean"}}, "animation": {"desc": "\n\n<p>是否开启动画。</p>\n", "uiControl": {"type": "boolean", "default": "true", "clean": "true"}}, "animationThreshold": {"desc": "<p>是否开启动画的阈值，当单个系列显示的图形数量大于这个阈值时会关闭动画。</p>\n"}, "animationDuration": {"desc": "\n\n<p>初始动画的时长，支持回调函数，可以通过每个数据返回不同的时长实现更戏剧的初始动画效果：</p>\n<pre><code class=\"lang-js\">animationDuration: function (idx) {\n    // 越往后的数据时长越大\n    return idx * 100;\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "default": "1000", "step": "20", "clean": "true"}}, "animationEasing": {"desc": "\n\n<p>初始动画的缓动效果。不同的缓动效果可以参考 <a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=line-easing\" target=\"_blank\">缓动示例</a>。</p>\n", "uiControl": {"type": "enum", "options": "linear,quadraticIn,quadraticOut,quadraticInOut,cubicIn,cubicOut,cubicInOut,quarticIn,quarticOut,quarticInOut,quinticIn,quinticOut,quinticInOut,sinusoidalIn,sinusoidalOut,sinusoidalInOut,exponentialIn,exponentialOut,exponentialInOut,circularIn,circularOut,circularInOut,elasticIn,elasticOut,elasticInOut,backIn,backOut,backInOut,bounceIn,bounceOut,bounceInOut", "clean": "true"}}, "animationDelay": {"desc": "<p>初始动画的延迟，支持回调函数，可以通过每个数据返回不同的 delay 时间实现更戏剧的初始动画效果。</p>\n<p>如下示例：</p>\n<pre><code class=\"lang-js\">animationDelay: function (idx) {\n    // 越往后的数据延迟越大\n    return idx * 100;\n}\n</code></pre>\n<p>也可以看<a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=bar-animation-delay\" target=\"_blank\">该示例</a></p>\n"}, "animationDurationUpdate": {"desc": "\n\n<p>数据更新动画的时长。</p>\n<p>支持回调函数，可以通过每个数据返回不同的时长实现更戏剧的更新动画效果：</p>\n<pre><code class=\"lang-js\">animationDurationUpdate: function (idx) {\n    // 越往后的数据时长越大\n    return idx * 100;\n}\n</code></pre>\n", "uiControl": {"type": "number", "min": "0", "default": "1000", "step": "20"}}, "animationEasingUpdate": {"desc": "\n\n<p>数据更新动画的缓动效果。</p>\n", "uiControl": {"type": "enum", "options": "linear,quadraticIn,quadraticOut,quadraticInOut,cubicIn,cubicOut,cubicInOut,quarticIn,quarticOut,quarticInOut,quinticIn,quinticOut,quinticInOut,sinusoidalIn,sinusoidalOut,sinusoidalInOut,exponentialIn,exponentialOut,exponentialInOut,circularIn,circularOut,circularInOut,elasticIn,elasticOut,elasticInOut,backIn,backOut,backInOut,bounceIn,bounceOut,bounceInOut"}}, "animationDelayUpdate": {"desc": "<p>数据更新动画的延迟，支持回调函数，可以通过每个数据返回不同的 delay 时间实现更戏剧的更新动画效果。</p>\n<p>如下示例：</p>\n<pre><code class=\"lang-js\">animationDelayUpdate: function (idx) {\n    // 越往后的数据延迟越大\n    return idx * 100;\n}\n</code></pre>\n<p>也可以看<a href=\"http://localhost/incubator-echarts-website/next/examples/zh/editor.html?c=bar-animation-delay\" target=\"_blank\">该示例</a></p>\n"}}