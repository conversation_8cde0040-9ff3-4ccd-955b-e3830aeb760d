export default {
  type: 'page',
  body: [
    {
      type: 'panel',
      title: '横向交替排列用法，含有卡片展示效果',
      body: {
        type: 'timeline',
        mode: 'alternate',
        direction: 'horizontal',
        items: [
          {
            cardSchema: {
              type: 'card',
              href: 'https://github.com/baidu/amis',
              header: {
                title: '标题',
                subTitle: '副标题',
                description: '这是一段描述',
                avatarText: 'AMIS'
              },
              body: '这里是内容'
            }
          },
          {
            time: '2019-02-08',
            title: '卡片组说明',
            detail:
              '卡片展示，不支持配置初始化接口初始化数据域，所以需要搭配类似像Service这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过source属性，获取数据链中的数据，完成数据展示。'
          },
          {
            time: '2019-02-09',
            title: '表格展现说明',
            detail:
              '通过表格的方式来展现数据，和 table 的不同之处：数据源要求不同：table 的数据源需要是多行的数据，最典型的就是来自某个数据库的表，table view 的数据源可以来自各种固定的数据，比如单元格的某一列是来自某个变量。功能不同：table 只能用来做数据表的展现，table view 除了展现复杂的报表，还能用来进行布局。合并单元格方式不同：table 的合并单元格需要依赖数据，table view 的合并单元格是手动指定的，因此可以支持不规则的数据格式。'
          }
        ]
      }
    },
    {
      type: 'panel',
      title: '横向时间底部排列用法，含有节点颜色及大小配置',
      body: {
        type: 'timeline',
        mode: 'bottom',
        direction: 'horizontal',
        items: [
          {
            time: '2019-02-07',
            dotSize: 'sm',
            align: 'left',
            color: 'info',
            title: '图片集说明',
            detail:
              '图片集展示，不支持配置初始化接口初始化数据域，所以需要搭配类似像Service、Form或CRUD这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过source属性，获取数据链中的数据，完成数据展示。'
          },
          {
            time: '2019-02-08',
            color: 'danger',
            align: 'left',
            title: '卡片组说明',
            detail:
              '卡片展示，不支持配置初始化接口初始化数据域，所以需要搭配类似像Service这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过source属性，获取数据链中的数据，完成数据展示。'
          },
          {
            time: '2019-02-09',
            color: 'warning',
            dotSize: 'lg',
            align: 'right',
            title: '宫格导航说明',
            detail:
              '宫格菜单导航，不支持配置初始化接口初始化数据域，所以需要搭配类似像Service、Form或CRUD这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过source属性，获取数据链中的数据，完成菜单展示。'
          },
          {
            time: '2019-02-10',
            color: 'success',
            align: 'right',
            dotSize: 'xl',
            title: '表格展现说明',
            detail:
              '通过表格的方式来展现数据，和 table 的不同之处：数据源要求不同：table 的数据源需要是多行的数据，最典型的就是来自某个数据库的表，table view 的数据源可以来自各种固定的数据，比如单元格的某一列是来自某个变量。功能不同：table 只能用来做数据表的展现，table view 除了展现复杂的报表，还能用来进行布局。合并单元格方式不同：table 的合并单元格需要依赖数据，table view 的合并单元格是手动指定的，因此可以支持不规则的数据格式。'
          },
          {
            time: '2019-02-11',
            color: 'success',
            align: 'right',
            dotSize: 'xl',
            backgroundColor: '#ff0606',
            title: '表格展现说明',
            detail:
              '通过表格的方式来展现数据，和 table 的不同之处：数据源要求不同：table 的数据源需要是多行的数据，最典型的就是来自某个数据库的表，table view 的数据源可以来自各种固定的数据，比如单元格的某一列是来自某个变量。功能不同：table 只能用来做数据表的展现，table view 除了展现复杂的报表，还能用来进行布局。合并单元格方式不同：table 的合并单元格需要依赖数据，table view 的合并单元格是手动指定的，因此可以支持不规则的数据格式。'
          }
        ]
      }
    },
    {
      type: 'panel',
      title: '横向时间顶部排列用法，含有全卡片展示以及节点位置配置',
      body: {
        type: 'timeline',
        direction: 'horizontal',
        items: [
          {
            time: '2019-02-07',
            dotSize: 'sm',
            color: 'info',
            align: 'right',
            cardSchema: {
              type: 'card',
              href: 'https://github.com/baidu/amis',
              header: {
                title: '标题',
                subTitle: '副标题',
                description: '这是一段描述',
                avatarText: 'AMIS'
              },
              body: '这里是内容'
            }
          },
          {
            time: '2019-02-08',
            color: 'danger',
            align: 'right',
            cardSchema: {
              type: 'card',
              href: 'https://github.com/baidu/amis',
              header: {
                title: '标题',
                subTitle: '副标题',
                description: '这是一段描述',
                avatarText: 'AMIS'
              },
              body: '这里是内容'
            }
          },
          {
            time: '2019-02-09',
            color: 'warning',
            dotSize: 'lg',
            align: 'right',
            cardSchema: {
              type: 'card',
              href: 'https://github.com/baidu/amis',
              header: {
                title: '标题',
                subTitle: '副标题',
                description: '这是一段描述',
                avatarText: 'AMIS'
              },
              body: '这里是内容'
            }
          },
          {
            time: '2019-02-10',
            color: 'success',
            dotSize: 'xl',
            align: 'right',
            cardSchema: {
              type: 'card',
              href: 'https://github.com/baidu/amis',
              header: {
                title: '标题',
                subTitle: '副标题',
                description: '这是一段描述',
                avatarText: 'AMIS'
              },
              body: '这里是内容'
            }
          }
        ]
      }
    },
    {
      type: 'panel',
      title: '横向时间底部排列用法，含有节点颜色及大小配置',
      body: {
        type: 'timeline',
        mode: 'bottom',
        direction: 'horizontal',
        items: [
          {
            time: '2019-02-07',
            dotSize: 'sm',
            align: 'left',
            icon: `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>填充颜色备份</title>
    <g id="编辑发布" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="2-0图片组件、文字组件" transform="translate(-412.000000, -1918.000000)">
            <g id="编组-51备份-12" transform="translate(400.000000, 1910.000000)">
                <g id="编组-2" transform="translate(12.000000, 8.000000)">
                    <rect id="矩形" fill="#E8E9EB" x="0" y="0" width="24" height="24" rx="6"></rect>
                    <g id="编组" transform="translate(4.500000, 4.500000)" fill="#2C2C2C" fill-rule="nonzero">
                        <path d="M7.37634969,15 C3.32617532,14.9448597 0.0562405919,11.6732467 0.00112856122,7.62099359 C-0.0671605286,3.65819387 2.97170374,0.310311323 6.93247063,0.00285272312 L7.13733788,0.00285272312 C7.9568069,-0.0313093382 8.70798682,0.241987185 9.28844405,0.788580263 C9.90304581,1.36933539 10.2444912,2.18922499 10.2444912,3.04327666 C10.2444912,3.14576287 10.2444912,3.24824907 10.2786358,3.35073527 C10.3810694,4.0339766 10.9615266,4.61473173 11.6444175,4.71721793 C11.7468511,4.75137999 11.8492847,4.75137999 11.9517183,4.75137999 C12.8053319,4.75137999 13.6248009,5.09300065 14.2052581,5.70791787 C14.7515708,6.28867301 15.0588717,7.04023847 14.9905826,7.86012807 L14.9905826,8.06510047 C14.7174263,11.9595761 11.4395502,15 7.51292785,15 L7.37634969,15 Z M7.03490426,1.36933539 C3.85946183,1.60846986 1.29862116,4.40975933 1.36691025,7.58683153 C1.43519934,10.900552 4.06432908,13.5651932 7.41049423,13.6335173 C10.6200812,13.6676794 13.3857891,11.1396865 13.6589455,7.96261427 L13.6589455,7.79180393 C13.69309,7.38185913 13.5223673,6.97191433 13.249211,6.66445573 C12.9419101,6.32283507 12.4638865,6.15202473 11.9858629,6.15202473 C11.8151402,6.15202473 11.6102729,6.15202473 11.4395502,6.11786267 C10.1762022,5.91289027 9.15186588,4.88802827 8.94699863,3.62403179 C8.87870954,3.38489733 8.87870954,3.21408699 8.87870954,3.04327666 C8.87870954,2.56500773 8.67384229,2.0867388 8.36654141,1.77928019 C8.05924052,1.50598365 7.68365055,1.33517332 7.3080606,1.33517332 L7.23977151,1.33517332 C7.17148242,1.33517332 7.10319335,1.33517332 7.03490426,1.36933539 Z M6.14714617,5.77624199 C5.39596624,5.77624199 4.78136447,5.16132479 4.78136447,4.40975933 C4.78136447,3.65819387 5.39596623,3.04327666 6.14714617,3.04327666 C6.89832611,3.04327666 7.51292785,3.65819387 7.51292785,4.40975933 C7.51292785,5.16132479 6.89832609,5.77624199 6.14714617,5.77624199 Z M5.80570074,9.53406933 C6.38615796,9.53406933 6.830037,9.97817619 6.830037,10.5589313 C6.830037,11.1396865 6.38615796,11.5837933 5.80570074,11.5837933 C5.22524351,11.5837933 4.78136447,11.1396865 4.78136447,10.5589313 C4.78136447,9.97817619 5.22524351,9.53406933 5.80570074,9.53406933 Z M4.09847363,8.68001767 C3.51801641,8.68001767 3.07413737,8.23591081 3.07413737,7.65515567 C3.07413737,7.07440053 3.51801641,6.63029367 4.09847363,6.63029367 C4.67893086,6.63029367 5.1228099,7.07440053 5.1228099,7.65515567 C5.1228099,8.23591081 4.67893086,8.68001767 4.09847363,8.68001767 Z" id="形状"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>`,
            title: '图片集说明',
            detail:
              '图片集展示，不支持配置初始化接口初始化数据域，所以需要搭配类似像Service、Form或CRUD这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过source属性，获取数据链中的数据，完成数据展示。'
          },
          {
            time: '2019-02-08',
            align: 'left',
            title: '卡片组说明',
            icon: `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>填充颜色备份</title>
    <g id="编辑发布" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="2-0图片组件、文字组件" transform="translate(-412.000000, -1918.000000)">
            <g id="编组-51备份-12" transform="translate(400.000000, 1910.000000)">
                <g id="编组-2" transform="translate(12.000000, 8.000000)">
                    <rect id="矩形" fill="#E8E9EB" x="0" y="0" width="24" height="24" rx="6"></rect>
                    <g id="编组" transform="translate(4.500000, 4.500000)" fill="#2C2C2C" fill-rule="nonzero">
                        <path d="M7.37634969,15 C3.32617532,14.9448597 0.0562405919,11.6732467 0.00112856122,7.62099359 C-0.0671605286,3.65819387 2.97170374,0.310311323 6.93247063,0.00285272312 L7.13733788,0.00285272312 C7.9568069,-0.0313093382 8.70798682,0.241987185 9.28844405,0.788580263 C9.90304581,1.36933539 10.2444912,2.18922499 10.2444912,3.04327666 C10.2444912,3.14576287 10.2444912,3.24824907 10.2786358,3.35073527 C10.3810694,4.0339766 10.9615266,4.61473173 11.6444175,4.71721793 C11.7468511,4.75137999 11.8492847,4.75137999 11.9517183,4.75137999 C12.8053319,4.75137999 13.6248009,5.09300065 14.2052581,5.70791787 C14.7515708,6.28867301 15.0588717,7.04023847 14.9905826,7.86012807 L14.9905826,8.06510047 C14.7174263,11.9595761 11.4395502,15 7.51292785,15 L7.37634969,15 Z M7.03490426,1.36933539 C3.85946183,1.60846986 1.29862116,4.40975933 1.36691025,7.58683153 C1.43519934,10.900552 4.06432908,13.5651932 7.41049423,13.6335173 C10.6200812,13.6676794 13.3857891,11.1396865 13.6589455,7.96261427 L13.6589455,7.79180393 C13.69309,7.38185913 13.5223673,6.97191433 13.249211,6.66445573 C12.9419101,6.32283507 12.4638865,6.15202473 11.9858629,6.15202473 C11.8151402,6.15202473 11.6102729,6.15202473 11.4395502,6.11786267 C10.1762022,5.91289027 9.15186588,4.88802827 8.94699863,3.62403179 C8.87870954,3.38489733 8.87870954,3.21408699 8.87870954,3.04327666 C8.87870954,2.56500773 8.67384229,2.0867388 8.36654141,1.77928019 C8.05924052,1.50598365 7.68365055,1.33517332 7.3080606,1.33517332 L7.23977151,1.33517332 C7.17148242,1.33517332 7.10319335,1.33517332 7.03490426,1.36933539 Z M6.14714617,5.77624199 C5.39596624,5.77624199 4.78136447,5.16132479 4.78136447,4.40975933 C4.78136447,3.65819387 5.39596623,3.04327666 6.14714617,3.04327666 C6.89832611,3.04327666 7.51292785,3.65819387 7.51292785,4.40975933 C7.51292785,5.16132479 6.89832609,5.77624199 6.14714617,5.77624199 Z M5.80570074,9.53406933 C6.38615796,9.53406933 6.830037,9.97817619 6.830037,10.5589313 C6.830037,11.1396865 6.38615796,11.5837933 5.80570074,11.5837933 C5.22524351,11.5837933 4.78136447,11.1396865 4.78136447,10.5589313 C4.78136447,9.97817619 5.22524351,9.53406933 5.80570074,9.53406933 Z M4.09847363,8.68001767 C3.51801641,8.68001767 3.07413737,8.23591081 3.07413737,7.65515567 C3.07413737,7.07440053 3.51801641,6.63029367 4.09847363,6.63029367 C4.67893086,6.63029367 5.1228099,7.07440053 5.1228099,7.65515567 C5.1228099,8.23591081 4.67893086,8.68001767 4.09847363,8.68001767 Z" id="形状"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>`,
            detail:
              '卡片展示，不支持配置初始化接口初始化数据域，所以需要搭配类似像Service这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过source属性，获取数据链中的数据，完成数据展示。'
          },
          {
            time: '2019-02-09',
            dotSize: 'lg',
            align: 'center',
            title: '宫格导航说明',
            icon: `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>填充颜色备份</title>
    <g id="编辑发布" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="2-0图片组件、文字组件" transform="translate(-412.000000, -1918.000000)">
            <g id="编组-51备份-12" transform="translate(400.000000, 1910.000000)">
                <g id="编组-2" transform="translate(12.000000, 8.000000)">
                    <rect id="矩形" fill="#E8E9EB" x="0" y="0" width="24" height="24" rx="6"></rect>
                    <g id="编组" transform="translate(4.500000, 4.500000)" fill="#2C2C2C" fill-rule="nonzero">
                        <path d="M7.37634969,15 C3.32617532,14.9448597 0.0562405919,11.6732467 0.00112856122,7.62099359 C-0.0671605286,3.65819387 2.97170374,0.310311323 6.93247063,0.00285272312 L7.13733788,0.00285272312 C7.9568069,-0.0313093382 8.70798682,0.241987185 9.28844405,0.788580263 C9.90304581,1.36933539 10.2444912,2.18922499 10.2444912,3.04327666 C10.2444912,3.14576287 10.2444912,3.24824907 10.2786358,3.35073527 C10.3810694,4.0339766 10.9615266,4.61473173 11.6444175,4.71721793 C11.7468511,4.75137999 11.8492847,4.75137999 11.9517183,4.75137999 C12.8053319,4.75137999 13.6248009,5.09300065 14.2052581,5.70791787 C14.7515708,6.28867301 15.0588717,7.04023847 14.9905826,7.86012807 L14.9905826,8.06510047 C14.7174263,11.9595761 11.4395502,15 7.51292785,15 L7.37634969,15 Z M7.03490426,1.36933539 C3.85946183,1.60846986 1.29862116,4.40975933 1.36691025,7.58683153 C1.43519934,10.900552 4.06432908,13.5651932 7.41049423,13.6335173 C10.6200812,13.6676794 13.3857891,11.1396865 13.6589455,7.96261427 L13.6589455,7.79180393 C13.69309,7.38185913 13.5223673,6.97191433 13.249211,6.66445573 C12.9419101,6.32283507 12.4638865,6.15202473 11.9858629,6.15202473 C11.8151402,6.15202473 11.6102729,6.15202473 11.4395502,6.11786267 C10.1762022,5.91289027 9.15186588,4.88802827 8.94699863,3.62403179 C8.87870954,3.38489733 8.87870954,3.21408699 8.87870954,3.04327666 C8.87870954,2.56500773 8.67384229,2.0867388 8.36654141,1.77928019 C8.05924052,1.50598365 7.68365055,1.33517332 7.3080606,1.33517332 L7.23977151,1.33517332 C7.17148242,1.33517332 7.10319335,1.33517332 7.03490426,1.36933539 Z M6.14714617,5.77624199 C5.39596624,5.77624199 4.78136447,5.16132479 4.78136447,4.40975933 C4.78136447,3.65819387 5.39596623,3.04327666 6.14714617,3.04327666 C6.89832611,3.04327666 7.51292785,3.65819387 7.51292785,4.40975933 C7.51292785,5.16132479 6.89832609,5.77624199 6.14714617,5.77624199 Z M5.80570074,9.53406933 C6.38615796,9.53406933 6.830037,9.97817619 6.830037,10.5589313 C6.830037,11.1396865 6.38615796,11.5837933 5.80570074,11.5837933 C5.22524351,11.5837933 4.78136447,11.1396865 4.78136447,10.5589313 C4.78136447,9.97817619 5.22524351,9.53406933 5.80570074,9.53406933 Z M4.09847363,8.68001767 C3.51801641,8.68001767 3.07413737,8.23591081 3.07413737,7.65515567 C3.07413737,7.07440053 3.51801641,6.63029367 4.09847363,6.63029367 C4.67893086,6.63029367 5.1228099,7.07440053 5.1228099,7.65515567 C5.1228099,8.23591081 4.67893086,8.68001767 4.09847363,8.68001767 Z" id="形状"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>`,
            detail:
              '宫格菜单导航，不支持配置初始化接口初始化数据域，所以需要搭配类似像Service、Form或CRUD这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过source属性，获取数据链中的数据，完成菜单展示。'
          },
          {
            time: '2019-02-10',
            icon: `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>填充颜色备份</title>
    <g id="编辑发布" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="2-0图片组件、文字组件" transform="translate(-412.000000, -1918.000000)">
            <g id="编组-51备份-12" transform="translate(400.000000, 1910.000000)">
                <g id="编组-2" transform="translate(12.000000, 8.000000)">
                    <rect id="矩形" fill="#E8E9EB" x="0" y="0" width="24" height="24" rx="6"></rect>
                    <g id="编组" transform="translate(4.500000, 4.500000)" fill="#2C2C2C" fill-rule="nonzero">
                        <path d="M7.37634969,15 C3.32617532,14.9448597 0.0562405919,11.6732467 0.00112856122,7.62099359 C-0.0671605286,3.65819387 2.97170374,0.310311323 6.93247063,0.00285272312 L7.13733788,0.00285272312 C7.9568069,-0.0313093382 8.70798682,0.241987185 9.28844405,0.788580263 C9.90304581,1.36933539 10.2444912,2.18922499 10.2444912,3.04327666 C10.2444912,3.14576287 10.2444912,3.24824907 10.2786358,3.35073527 C10.3810694,4.0339766 10.9615266,4.61473173 11.6444175,4.71721793 C11.7468511,4.75137999 11.8492847,4.75137999 11.9517183,4.75137999 C12.8053319,4.75137999 13.6248009,5.09300065 14.2052581,5.70791787 C14.7515708,6.28867301 15.0588717,7.04023847 14.9905826,7.86012807 L14.9905826,8.06510047 C14.7174263,11.9595761 11.4395502,15 7.51292785,15 L7.37634969,15 Z M7.03490426,1.36933539 C3.85946183,1.60846986 1.29862116,4.40975933 1.36691025,7.58683153 C1.43519934,10.900552 4.06432908,13.5651932 7.41049423,13.6335173 C10.6200812,13.6676794 13.3857891,11.1396865 13.6589455,7.96261427 L13.6589455,7.79180393 C13.69309,7.38185913 13.5223673,6.97191433 13.249211,6.66445573 C12.9419101,6.32283507 12.4638865,6.15202473 11.9858629,6.15202473 C11.8151402,6.15202473 11.6102729,6.15202473 11.4395502,6.11786267 C10.1762022,5.91289027 9.15186588,4.88802827 8.94699863,3.62403179 C8.87870954,3.38489733 8.87870954,3.21408699 8.87870954,3.04327666 C8.87870954,2.56500773 8.67384229,2.0867388 8.36654141,1.77928019 C8.05924052,1.50598365 7.68365055,1.33517332 7.3080606,1.33517332 L7.23977151,1.33517332 C7.17148242,1.33517332 7.10319335,1.33517332 7.03490426,1.36933539 Z M6.14714617,5.77624199 C5.39596624,5.77624199 4.78136447,5.16132479 4.78136447,4.40975933 C4.78136447,3.65819387 5.39596623,3.04327666 6.14714617,3.04327666 C6.89832611,3.04327666 7.51292785,3.65819387 7.51292785,4.40975933 C7.51292785,5.16132479 6.89832609,5.77624199 6.14714617,5.77624199 Z M5.80570074,9.53406933 C6.38615796,9.53406933 6.830037,9.97817619 6.830037,10.5589313 C6.830037,11.1396865 6.38615796,11.5837933 5.80570074,11.5837933 C5.22524351,11.5837933 4.78136447,11.1396865 4.78136447,10.5589313 C4.78136447,9.97817619 5.22524351,9.53406933 5.80570074,9.53406933 Z M4.09847363,8.68001767 C3.51801641,8.68001767 3.07413737,8.23591081 3.07413737,7.65515567 C3.07413737,7.07440053 3.51801641,6.63029367 4.09847363,6.63029367 C4.67893086,6.63029367 5.1228099,7.07440053 5.1228099,7.65515567 C5.1228099,8.23591081 4.67893086,8.68001767 4.09847363,8.68001767 Z" id="形状"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>`,
            align: 'right',
            dotSize: 'xl',
            title: '表格展现说明',
            detail:
              '通过表格的方式来展现数据，和 table 的不同之处：数据源要求不同：table 的数据源需要是多行的数据，最典型的就是来自某个数据库的表，table view 的数据源可以来自各种固定的数据，比如单元格的某一列是来自某个变量。功能不同：table 只能用来做数据表的展现，table view 除了展现复杂的报表，还能用来进行布局。合并单元格方式不同：table 的合并单元格需要依赖数据，table view 的合并单元格是手动指定的，因此可以支持不规则的数据格式。'
          },
          {
            time: '2019-02-11',
            icon: `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>填充颜色备份</title>
    <g id="编辑发布" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="2-0图片组件、文字组件" transform="translate(-412.000000, -1918.000000)">
            <g id="编组-51备份-12" transform="translate(400.000000, 1910.000000)">
                <g id="编组-2" transform="translate(12.000000, 8.000000)">
                    <rect id="矩形" fill="#E8E9EB" x="0" y="0" width="24" height="24" rx="6"></rect>
                    <g id="编组" transform="translate(4.500000, 4.500000)" fill="#2C2C2C" fill-rule="nonzero">
                        <path d="M7.37634969,15 C3.32617532,14.9448597 0.0562405919,11.6732467 0.00112856122,7.62099359 C-0.0671605286,3.65819387 2.97170374,0.310311323 6.93247063,0.00285272312 L7.13733788,0.00285272312 C7.9568069,-0.0313093382 8.70798682,0.241987185 9.28844405,0.788580263 C9.90304581,1.36933539 10.2444912,2.18922499 10.2444912,3.04327666 C10.2444912,3.14576287 10.2444912,3.24824907 10.2786358,3.35073527 C10.3810694,4.0339766 10.9615266,4.61473173 11.6444175,4.71721793 C11.7468511,4.75137999 11.8492847,4.75137999 11.9517183,4.75137999 C12.8053319,4.75137999 13.6248009,5.09300065 14.2052581,5.70791787 C14.7515708,6.28867301 15.0588717,7.04023847 14.9905826,7.86012807 L14.9905826,8.06510047 C14.7174263,11.9595761 11.4395502,15 7.51292785,15 L7.37634969,15 Z M7.03490426,1.36933539 C3.85946183,1.60846986 1.29862116,4.40975933 1.36691025,7.58683153 C1.43519934,10.900552 4.06432908,13.5651932 7.41049423,13.6335173 C10.6200812,13.6676794 13.3857891,11.1396865 13.6589455,7.96261427 L13.6589455,7.79180393 C13.69309,7.38185913 13.5223673,6.97191433 13.249211,6.66445573 C12.9419101,6.32283507 12.4638865,6.15202473 11.9858629,6.15202473 C11.8151402,6.15202473 11.6102729,6.15202473 11.4395502,6.11786267 C10.1762022,5.91289027 9.15186588,4.88802827 8.94699863,3.62403179 C8.87870954,3.38489733 8.87870954,3.21408699 8.87870954,3.04327666 C8.87870954,2.56500773 8.67384229,2.0867388 8.36654141,1.77928019 C8.05924052,1.50598365 7.68365055,1.33517332 7.3080606,1.33517332 L7.23977151,1.33517332 C7.17148242,1.33517332 7.10319335,1.33517332 7.03490426,1.36933539 Z M6.14714617,5.77624199 C5.39596624,5.77624199 4.78136447,5.16132479 4.78136447,4.40975933 C4.78136447,3.65819387 5.39596623,3.04327666 6.14714617,3.04327666 C6.89832611,3.04327666 7.51292785,3.65819387 7.51292785,4.40975933 C7.51292785,5.16132479 6.89832609,5.77624199 6.14714617,5.77624199 Z M5.80570074,9.53406933 C6.38615796,9.53406933 6.830037,9.97817619 6.830037,10.5589313 C6.830037,11.1396865 6.38615796,11.5837933 5.80570074,11.5837933 C5.22524351,11.5837933 4.78136447,11.1396865 4.78136447,10.5589313 C4.78136447,9.97817619 5.22524351,9.53406933 5.80570074,9.53406933 Z M4.09847363,8.68001767 C3.51801641,8.68001767 3.07413737,8.23591081 3.07413737,7.65515567 C3.07413737,7.07440053 3.51801641,6.63029367 4.09847363,6.63029367 C4.67893086,6.63029367 5.1228099,7.07440053 5.1228099,7.65515567 C5.1228099,8.23591081 4.67893086,8.68001767 4.09847363,8.68001767 Z" id="形状"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>`,
            align: 'right',
            dotSize: 'xl',
            backgroundColor: '#ff0606',
            title: '表格展现说明',
            detail:
              '通过表格的方式来展现数据，和 table 的不同之处：数据源要求不同：table 的数据源需要是多行的数据，最典型的就是来自某个数据库的表，table view 的数据源可以来自各种固定的数据，比如单元格的某一列是来自某个变量。功能不同：table 只能用来做数据表的展现，table view 除了展现复杂的报表，还能用来进行布局。合并单元格方式不同：table 的合并单元格需要依赖数据，table view 的合并单元格是手动指定的，因此可以支持不规则的数据格式。'
          }
        ]
      }
    },
    {
      type: 'panel',
      title: '纵向卡片排布，包含icon图标配置',
      body: {
        type: 'timeline',
        direction: 'vertical',
        mode: 'alternate',
        items: [
          {
            time: '2019-02-07',
            align: 'bottom',
            cardSchema: {
              type: 'card',
              href: 'https://github.com/baidu/amis',
              header: {
                title: '标题',
                subTitle: '副标题',
                description: '这是一段描述',
                avatarText: 'AMIS'
              },
              body: '这里是内容'
            },
            icon: 'fa fa-first-order',
            dotSize: 'lg'
          },
          {
            time: '2019-02-08',
            align: 'top',
            icon: 'fa fa-adn',
            cardSchema: {
              type: 'card',
              href: 'https://github.com/baidu/amis',
              header: {
                title: '标题',
                subTitle: '副标题',
                description: '这是一段描述',
                avatarText: 'AMIS'
              },
              body: '这里是内容'
            }
          },
          {
            time: '2019-02-08',
            title: '节点标题',
            detail: 'success',
            dotSize: 'lg',
            icon: `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>填充颜色备份</title>
    <g id="编辑发布" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="2-0图片组件、文字组件" transform="translate(-412.000000, -1918.000000)">
            <g id="编组-51备份-12" transform="translate(400.000000, 1910.000000)">
                <g id="编组-2" transform="translate(12.000000, 8.000000)">
                    <rect id="矩形" fill="#E8E9EB" x="0" y="0" width="24" height="24" rx="6"></rect>
                    <g id="编组" transform="translate(4.500000, 4.500000)" fill="#2C2C2C" fill-rule="nonzero">
                        <path d="M7.37634969,15 C3.32617532,14.9448597 0.0562405919,11.6732467 0.00112856122,7.62099359 C-0.0671605286,3.65819387 2.97170374,0.310311323 6.93247063,0.00285272312 L7.13733788,0.00285272312 C7.9568069,-0.0313093382 8.70798682,0.241987185 9.28844405,0.788580263 C9.90304581,1.36933539 10.2444912,2.18922499 10.2444912,3.04327666 C10.2444912,3.14576287 10.2444912,3.24824907 10.2786358,3.35073527 C10.3810694,4.0339766 10.9615266,4.61473173 11.6444175,4.71721793 C11.7468511,4.75137999 11.8492847,4.75137999 11.9517183,4.75137999 C12.8053319,4.75137999 13.6248009,5.09300065 14.2052581,5.70791787 C14.7515708,6.28867301 15.0588717,7.04023847 14.9905826,7.86012807 L14.9905826,8.06510047 C14.7174263,11.9595761 11.4395502,15 7.51292785,15 L7.37634969,15 Z M7.03490426,1.36933539 C3.85946183,1.60846986 1.29862116,4.40975933 1.36691025,7.58683153 C1.43519934,10.900552 4.06432908,13.5651932 7.41049423,13.6335173 C10.6200812,13.6676794 13.3857891,11.1396865 13.6589455,7.96261427 L13.6589455,7.79180393 C13.69309,7.38185913 13.5223673,6.97191433 13.249211,6.66445573 C12.9419101,6.32283507 12.4638865,6.15202473 11.9858629,6.15202473 C11.8151402,6.15202473 11.6102729,6.15202473 11.4395502,6.11786267 C10.1762022,5.91289027 9.15186588,4.88802827 8.94699863,3.62403179 C8.87870954,3.38489733 8.87870954,3.21408699 8.87870954,3.04327666 C8.87870954,2.56500773 8.67384229,2.0867388 8.36654141,1.77928019 C8.05924052,1.50598365 7.68365055,1.33517332 7.3080606,1.33517332 L7.23977151,1.33517332 C7.17148242,1.33517332 7.10319335,1.33517332 7.03490426,1.36933539 Z M6.14714617,5.77624199 C5.39596624,5.77624199 4.78136447,5.16132479 4.78136447,4.40975933 C4.78136447,3.65819387 5.39596623,3.04327666 6.14714617,3.04327666 C6.89832611,3.04327666 7.51292785,3.65819387 7.51292785,4.40975933 C7.51292785,5.16132479 6.89832609,5.77624199 6.14714617,5.77624199 Z M5.80570074,9.53406933 C6.38615796,9.53406933 6.830037,9.97817619 6.830037,10.5589313 C6.830037,11.1396865 6.38615796,11.5837933 5.80570074,11.5837933 C5.22524351,11.5837933 4.78136447,11.1396865 4.78136447,10.5589313 C4.78136447,9.97817619 5.22524351,9.53406933 5.80570074,9.53406933 Z M4.09847363,8.68001767 C3.51801641,8.68001767 3.07413737,8.23591081 3.07413737,7.65515567 C3.07413737,7.07440053 3.51801641,6.63029367 4.09847363,6.63029367 C4.67893086,6.63029367 5.1228099,7.07440053 5.1228099,7.65515567 C5.1228099,8.23591081 4.67893086,8.68001767 4.09847363,8.68001767 Z" id="形状"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>`,
            align: 'center'
          }
        ]
      }
    },
    {
      type: 'panel',
      title: '纵向左侧排布，包含icon隐藏和连线颜色配置',
      body: {
        type: 'timeline',
        direction: 'vertical',
        mode: 'left',
        items: [
          {
            time: '2019-02-07',
            dotSize: 'sm',
            color: 'info',
            hideDot: true,
            lineColor: '#2e86d3',
            title: '图片集说明',
            detail:
              '图片集展示，不支持配置初始化接口初始化数据域，所以需要搭配类似像Service、Form或CRUD这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过source属性，获取数据链中的数据，完成数据展示。'
          },
          {
            time: '2019-02-08',
            color: 'danger',
            lineColor: '#2ee414',
            title: '卡片组说明',
            detail:
              '卡片展示，不支持配置初始化接口初始化数据域，所以需要搭配类似像Service这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过source属性，获取数据链中的数据，完成数据展示。'
          },
          {
            time: '2019-02-09',
            color: 'warning',
            dotSize: 'lg',
            lineColor: '#e45c14',
            title: '宫格导航说明',
            detail:
              '宫格菜单导航，不支持配置初始化接口初始化数据域，所以需要搭配类似像Service、Form或CRUD这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过source属性，获取数据链中的数据，完成菜单展示。'
          },
          {
            time: '2019-02-10',
            color: 'success',
            dotSize: 'xl',
            lineColor: '#e4148a',
            hideDot: true,
            title: '表格展现说明',
            detail:
              '通过表格的方式来展现数据，和 table 的不同之处：数据源要求不同：table 的数据源需要是多行的数据，最典型的就是来自某个数据库的表，table view 的数据源可以来自各种固定的数据，比如单元格的某一列是来自某个变量。功能不同：table 只能用来做数据表的展现，table view 除了展现复杂的报表，还能用来进行布局。合并单元格方式不同：table 的合并单元格需要依赖数据，table view 的合并单元格是手动指定的，因此可以支持不规则的数据格式。'
          }
        ]
      }
    },
    {
      type: 'panel',
      title: '纵向右侧卡片排布',
      body: {
        type: 'timeline',
        direction: 'vertical',
        mode: 'right',
        items: [
          {
            time: '2019-02-07',
            dotSize: 'sm',
            color: 'info',
            align: 'right',
            cardSchema: {
              type: 'card',
              href: 'https://github.com/baidu/amis',
              header: {
                title: '标题',
                subTitle: '副标题',
                description: '这是一段描述',
                avatarText: 'AMIS'
              },
              body: '这里是内容'
            }
          },
          {
            time: '2019-02-08',
            color: 'danger',
            align: 'right',
            cardSchema: {
              type: 'card',
              href: 'https://github.com/baidu/amis',
              header: {
                title: '标题',
                subTitle: '副标题',
                description: '这是一段描述',
                avatarText: 'AMIS'
              },
              body: '这里是内容'
            }
          },
          {
            time: '2019-02-09',
            color: 'warning',
            dotSize: 'lg',
            align: 'right',
            cardSchema: {
              type: 'card',
              href: 'https://github.com/baidu/amis',
              header: {
                title: '标题',
                subTitle: '副标题',
                description: '这是一段描述',
                avatarText: 'AMIS'
              },
              body: '这里是内容'
            }
          },
          {
            time: '2019-02-10',
            color: 'success',
            dotSize: 'xl',
            align: 'right',
            cardSchema: {
              type: 'card',
              href: 'https://github.com/baidu/amis',
              header: {
                title: '标题',
                subTitle: '副标题',
                description: '这是一段描述',
                avatarText: 'AMIS'
              },
              body: '这里是内容'
            }
          }
        ]
      }
    }
  ]
};
