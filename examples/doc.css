.markdown-body {
  box-sizing: border-box;
  min-width: 200px;
  max-width: 980px;
  min-height: unset;
}

@media (max-width: 767px) {
  .markdown-body {
    padding: 15px;
  }
}

.markdown {
  padding: 25px 45px 0;
}

.doc-play-ground {
  height: auto;
  position: relative;
  width: 100%;
  margin: 20px 0;
  --affix-offset-top: 70px;
}

.doc-play-ground > .visibility-sensor {
  position: relative;
  width: 100%;
  height: 100%;
}

.doc-play-ground .nav-tabs {
  margin-bottom: 0;
}

.markdown-body > .amis-preview {
  margin-bottom: 15px;
}

.amis-doc {
  margin: 20px 0;
}

.amis-doc > .preview {
  padding-top: 3rem;
  padding-bottom: 3rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  overflow: hidden;
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
  background-color: #dbeafe;
}

body.dark .amis-doc > .preview {
  background: #191c22;
  color: #fff;
}

.amis-doc > pre {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}
