<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <title>amis - 低代码前端框架</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link type="image/x-icon" rel="shortcut icon" href="./static/favicon.png" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1"
    />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <link rel="stylesheet" href="./static/iconfont.css" />
    <link rel="stylesheet" href="./static/officefont.css" />
    <link rel="stylesheet" href="@fortawesome/fontawesome-free/css/all.css" />
    <link
      rel="stylesheet"
      href="@fortawesome/fontawesome-free/css/v4-shims.css"
    />
    <link rel="stylesheet" href="prismjs/themes/prism.css" />
    <!--DEPENDENCIES_INJECT_PLACEHOLDER-->
    <link
      rel="stylesheet"
      href="../node_modules/github-markdown-css/github-markdown-light.css"
    />
    <link rel="stylesheet" href="./doc.css" />

    <link rel="stylesheet" href="./style.scss" />
    <!--STYLE_PLACEHOLDER-->
    <style>
      .app-wrapper,
      .schema-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
      }
    </style>
    <script type="text/x-jsx">
      // gitee 最近特别慢
      if (location.hostname === 'baidu.gitee.io') {
        location.hostname = 'aisuda.bce.baidu.com';
      }

      let theme = localStorage.getItem('amis-theme') || 'cxd';
      if (theme === 'default') {
        theme = 'cxd';
      }

      // 非 IE 模式
      if (window.navigator.userAgent.indexOf('Trident') === -1) {
        document.write(
          `<link rel="stylesheet" title="ang" ${
            theme !== 'ang' ? 'disabled' : ''
          }  href="${__uri('amis/lib/themes/ang.css')}" />`
        );
        document.write(
          `<link rel="stylesheet" title="cxd" ${
            theme !== 'cxd' ? 'disabled' : ''
          } href="${__uri('amis/lib/themes/cxd.css')}" />`
        );
        document.write(
          `<link rel="stylesheet" title="dark" ${
            theme !== 'dark' ? 'disabled' : ''
          } href="${__uri('amis/lib/themes/dark.css')}" />`
        );
        document.write(
          `<link rel="stylesheet" title="antd" ${
            theme !== 'antd' ? 'disabled' : ''
          } href="${__uri('amis/lib/themes/antd.css')}" />`
        );
      } else {
        document.write(
          `<link rel="stylesheet" title="ang" ${
            theme !== 'ang' ? 'disabled' : ''
          }  href="${__uri('amis/lib/themes/ang-ie11.css')}" />`
        );
        document.write(
          `<link rel="stylesheet" title="cxd" ${
            theme !== 'cxd' ? 'disabled' : ''
          } href="${__uri('amis/lib/themes/cxd-ie11.css')}" />`
        );
        document.write(
          `<link rel="stylesheet" title="dark" ${
            theme !== 'dark' ? 'disabled' : ''
          } href="${__uri('amis/lib/themes/dark-ie11.css')}" />`
        );
        document.write(
          `<link rel="stylesheet" title="antd" ${
            theme !== 'antd' ? 'disabled' : ''
          } href="${__uri('amis/lib/themes/antd-ie11.css')}" />`
        );
      }
    </script>
    <!--ignore-->
    <link rel="stylesheet" href="amis/lib/helper.css" />
    <!--ignore-->
  </head>

  <body>
    <div id="root" class="app-wrapper"></div>
    <script src="./mod.js"></script>
    <script type="text/javascript">
      if (location.hostname !== 'localhost') {
        var _hmt = _hmt || [];

        // 百度统计
        (function () {
          var hm = document.createElement('script');
          hm.src =
            'https://hm.baidu.com/hm.js?286766a21abb57abefedbd5257a26dc8';
          var s = document.getElementsByTagName('script')[0];
          s.parentNode.insertBefore(hm, s);
        })();
      }

      window.enableAMISDebug = true;

      /* @require ./index.jsx 标记为同步依赖，提前加载 */
      amis.require(['./index.jsx'], function (app) {
        var initialState = {};
        app.bootstrap(document.getElementById('root'), initialState);
      });
    </script>
  </body>
</html>
