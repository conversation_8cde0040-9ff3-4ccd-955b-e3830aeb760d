// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`doAction:formItem validate 1`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验name
            </span>
          </button>
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验email
            </span>
          </button>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_info"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证信息：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_info"
                  placeholder=""
                  size="10"
                  type="text"
                  value="{
  "error": "这是必填项"
}"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_res"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证结果：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_res"
                  placeholder=""
                  size="10"
                  type="text"
                  value="validate name fail"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Panel cxd-Panel--default cxd-Panel--form"
            data-role="container"
          >
            <div
              class="cxd-Panel-heading"
            >
              <h3
                class="cxd-Panel-title"
              >
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    表单
                  </span>
                </span>
              </h3>
            </div>
            <div
              class="cxd-Panel-body"
            >
              <form
                class="cxd-Form cxd-Form--normal"
                novalidate=""
              >
                <input
                  style="display: none;"
                  type="submit"
                />
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-error is-required has-error--isRequired"
                  data-amis-name="name"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          姓名：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control is-error has-error--isRequired cxd-TextControl is-focused"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="name"
                        placeholder=""
                        size="10"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                  <ul
                    class="cxd-Form-feedback"
                  >
                    <li>
                      这是必填项
                    </li>
                  </ul>
                </div>
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-required"
                  data-amis-name="email"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          邮箱：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control cxd-TextControl"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="email"
                        placeholder=""
                        size="10"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div
              class="cxd-Panel-footerWrap"
            >
              <div
                class="cxd-Panel-btnToolbar cxd-Panel-footer"
              >
                <button
                  class="cxd-Button cxd-Button--primary cxd-Button--size-default"
                  type="submit"
                >
                  <span>
                    提交
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`doAction:formItem validate 2`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验name
            </span>
          </button>
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验email
            </span>
          </button>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_info"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证信息：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_info"
                  placeholder=""
                  size="10"
                  type="text"
                  value="{
  "error": "这是必填项"
}"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_res"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证结果：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_res"
                  placeholder=""
                  size="10"
                  type="text"
                  value="validate email fail"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Panel cxd-Panel--default cxd-Panel--form"
            data-role="container"
          >
            <div
              class="cxd-Panel-heading"
            >
              <h3
                class="cxd-Panel-title"
              >
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    表单
                  </span>
                </span>
              </h3>
            </div>
            <div
              class="cxd-Panel-body"
            >
              <form
                class="cxd-Form cxd-Form--normal"
                novalidate=""
              >
                <input
                  style="display: none;"
                  type="submit"
                />
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-error is-required has-error--isRequired"
                  data-amis-name="name"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          姓名：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control is-error has-error--isRequired cxd-TextControl"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="name"
                        placeholder=""
                        size="10"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                  <ul
                    class="cxd-Form-feedback"
                  >
                    <li>
                      这是必填项
                    </li>
                  </ul>
                </div>
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-error is-required has-error--isRequired"
                  data-amis-name="email"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          邮箱：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control is-error has-error--isRequired cxd-TextControl is-focused"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="email"
                        placeholder=""
                        size="10"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                  <ul
                    class="cxd-Form-feedback"
                  >
                    <li>
                      这是必填项
                    </li>
                  </ul>
                </div>
              </form>
            </div>
            <div
              class="cxd-Panel-footerWrap"
            >
              <div
                class="cxd-Panel-btnToolbar cxd-Panel-footer"
              >
                <button
                  class="cxd-Button cxd-Button--primary cxd-Button--size-default"
                  type="submit"
                >
                  <span>
                    提交
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`doAction:formItem validate 3`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验name
            </span>
          </button>
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验email
            </span>
          </button>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_info"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证信息：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_info"
                  placeholder=""
                  size="10"
                  type="text"
                  value="{
  "error": "Email 格式不正确",
  "value": "invalid_email"
}"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_res"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证结果：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_res"
                  placeholder=""
                  size="10"
                  type="text"
                  value="validate email fail"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Panel cxd-Panel--default cxd-Panel--form"
            data-role="container"
          >
            <div
              class="cxd-Panel-heading"
            >
              <h3
                class="cxd-Panel-title"
              >
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    表单
                  </span>
                </span>
              </h3>
            </div>
            <div
              class="cxd-Panel-body"
            >
              <form
                class="cxd-Form cxd-Form--normal"
                novalidate=""
              >
                <input
                  style="display: none;"
                  type="submit"
                />
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-error is-required has-error--isRequired"
                  data-amis-name="name"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          姓名：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control is-error has-error--isRequired cxd-TextControl"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="name"
                        placeholder=""
                        size="10"
                        type="text"
                        value=""
                      />
                    </div>
                  </div>
                  <ul
                    class="cxd-Form-feedback"
                  >
                    <li>
                      这是必填项
                    </li>
                  </ul>
                </div>
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-error is-required has-error--isEmail"
                  data-amis-name="email"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          邮箱：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control is-error has-error--isEmail cxd-TextControl is-focused"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="email"
                        placeholder=""
                        size="10"
                        type="text"
                        value="invalid_email"
                      />
                    </div>
                  </div>
                  <ul
                    class="cxd-Form-feedback"
                  >
                    <li>
                      Email 格式不正确
                    </li>
                  </ul>
                </div>
              </form>
            </div>
            <div
              class="cxd-Panel-footerWrap"
            >
              <div
                class="cxd-Panel-btnToolbar cxd-Panel-footer"
              >
                <button
                  class="cxd-Button cxd-Button--primary cxd-Button--size-default"
                  type="submit"
                >
                  <span>
                    提交
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`doAction:formItem validate 4`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验name
            </span>
          </button>
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验email
            </span>
          </button>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_info"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证信息：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_info"
                  placeholder=""
                  size="10"
                  type="text"
                  value="{
  "error": "Email 格式不正确",
  "value": "invalid_email"
}"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_res"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证结果：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_res"
                  placeholder=""
                  size="10"
                  type="text"
                  value="validate email success"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Panel cxd-Panel--default cxd-Panel--form"
            data-role="container"
          >
            <div
              class="cxd-Panel-heading"
            >
              <h3
                class="cxd-Panel-title"
              >
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    表单
                  </span>
                </span>
              </h3>
            </div>
            <div
              class="cxd-Panel-body"
            >
              <form
                class="cxd-Form cxd-Form--normal"
                novalidate=""
              >
                <input
                  style="display: none;"
                  type="submit"
                />
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-required"
                  data-amis-name="name"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          姓名：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control cxd-TextControl"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="name"
                        placeholder=""
                        size="10"
                        type="text"
                        value="amis"
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-required"
                  data-amis-name="email"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          邮箱：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control cxd-TextControl is-focused"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="email"
                        placeholder=""
                        size="10"
                        type="text"
                        value="<EMAIL>"
                      />
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div
              class="cxd-Panel-footerWrap"
            >
              <div
                class="cxd-Panel-btnToolbar cxd-Panel-footer"
              >
                <button
                  class="cxd-Button cxd-Button--primary cxd-Button--size-default"
                  type="submit"
                >
                  <span>
                    提交
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`doAction:formItem validate 5`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验name
            </span>
          </button>
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验email
            </span>
          </button>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_info"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证信息：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_info"
                  placeholder=""
                  size="10"
                  type="text"
                  value="{
  "error": "",
  "value": "amis"
}"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_res"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证结果：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_res"
                  placeholder=""
                  size="10"
                  type="text"
                  value="validate name success"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Panel cxd-Panel--default cxd-Panel--form"
            data-role="container"
          >
            <div
              class="cxd-Panel-heading"
            >
              <h3
                class="cxd-Panel-title"
              >
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    表单
                  </span>
                </span>
              </h3>
            </div>
            <div
              class="cxd-Panel-body"
            >
              <form
                class="cxd-Form cxd-Form--normal"
                novalidate=""
              >
                <input
                  style="display: none;"
                  type="submit"
                />
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-required"
                  data-amis-name="name"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          姓名：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control cxd-TextControl"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="name"
                        placeholder=""
                        size="10"
                        type="text"
                        value="amis"
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-required"
                  data-amis-name="email"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          邮箱：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control cxd-TextControl is-focused"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="email"
                        placeholder=""
                        size="10"
                        type="text"
                        value="<EMAIL>"
                      />
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div
              class="cxd-Panel-footerWrap"
            >
              <div
                class="cxd-Panel-btnToolbar cxd-Panel-footer"
              >
                <button
                  class="cxd-Button cxd-Button--primary cxd-Button--size-default"
                  type="submit"
                >
                  <span>
                    提交
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`doAction:formItem validate 6`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验name
            </span>
          </button>
          <button
            class="cxd-Button cxd-Button--default cxd-Button--size-default"
            type="button"
          >
            <span>
              校验email
            </span>
          </button>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_info"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证信息：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_info"
                  placeholder=""
                  size="10"
                  type="text"
                  value="{
  "error": "",
  "value": "<EMAIL>"
}"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Form-item cxd-Form-item--normal"
            data-amis-name="validate_res"
            data-role="form-item"
          >
            <label
              class="cxd-Form-label"
            >
              <span>
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    验证结果：
                  </span>
                </span>
              </span>
            </label>
            <div
              class="cxd-Form-control cxd-TextControl"
            >
              <div
                class="cxd-TextControl-input"
              >
                <input
                  autocomplete="off"
                  class=""
                  name="validate_res"
                  placeholder=""
                  size="10"
                  type="text"
                  value="validate email success"
                />
              </div>
            </div>
          </div>
          <div
            class="cxd-Panel cxd-Panel--default cxd-Panel--form"
            data-role="container"
          >
            <div
              class="cxd-Panel-heading"
            >
              <h3
                class="cxd-Panel-title"
              >
                <span
                  class="cxd-TplField fr-view"
                >
                  <span>
                    表单
                  </span>
                </span>
              </h3>
            </div>
            <div
              class="cxd-Panel-body"
            >
              <form
                class="cxd-Form cxd-Form--normal"
                novalidate=""
              >
                <input
                  style="display: none;"
                  type="submit"
                />
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-required"
                  data-amis-name="name"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          姓名：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control cxd-TextControl"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="name"
                        placeholder=""
                        size="10"
                        type="text"
                        value="amis"
                      />
                    </div>
                  </div>
                </div>
                <div
                  class="cxd-Form-item cxd-Form-item--normal is-required"
                  data-amis-name="email"
                  data-role="form-item"
                >
                  <label
                    class="cxd-Form-label"
                  >
                    <span>
                      <span
                        class="cxd-TplField fr-view"
                      >
                        <span>
                          邮箱：
                        </span>
                      </span>
                      <span
                        class="cxd-Form-star"
                      >
                        *
                      </span>
                    </span>
                  </label>
                  <div
                    class="cxd-Form-control cxd-TextControl is-focused"
                  >
                    <div
                      class="cxd-TextControl-input"
                    >
                      <input
                        autocomplete="off"
                        class=""
                        name="email"
                        placeholder=""
                        size="10"
                        type="text"
                        value="<EMAIL>"
                      />
                    </div>
                  </div>
                </div>
              </form>
            </div>
            <div
              class="cxd-Panel-footerWrap"
            >
              <div
                class="cxd-Panel-btnToolbar cxd-Panel-footer"
              >
                <button
                  class="cxd-Button cxd-Button--primary cxd-Button--size-default"
                  type="submit"
                >
                  <span>
                    提交
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
