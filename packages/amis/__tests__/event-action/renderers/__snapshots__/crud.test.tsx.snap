// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`doAction:crud reload 1`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--primary cxd-Button--size-default mb-2"
            type="button"
          >
            <span>
              刷新CRUD数据加载请求
            </span>
          </button>
          <div
            class="cxd-Crud"
            data-id="crud_reload"
            data-role="container"
          >
            <div
              class="cxd-Table cxd-Crud-body"
              data-id="crud_reload"
              style="--Table-thead-height: 0px; --Table-column-3-width: 0px; --Table-column-4-width: 0px; --Table-column-5-width: 0px; --Table-column-6-width: 0px; --Table-column-7-width: 0px; --Table-column-8-width: 0px; --Table-column-9-width: 0px;"
            >
              <div
                class="cxd-Table-fixedTop"
              >
                <div
                  class="cxd-Table-toolbar cxd-Table-headToolbar"
                >
                  <div
                    class="cxd-Table-actions"
                  >
                    <div
                      class="cxd-ColumnToggler"
                    >
                      <button
                        class="cxd-Button cxd-Button--default cxd-Button--size-sm"
                      >
                        <icon-mock
                          classname="icon icon-columns"
                          icon="columns"
                        />
                        <span
                          class="cxd-ColumnToggler-caret"
                        >
                          <icon-mock
                            classname="icon icon-right-arrow-bold"
                            icon="right-arrow-bold"
                          />
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="cxd-Table-contentWrap"
              >
                <div
                  class="cxd-Table-content"
                >
                  <table
                    class="cxd-Table-table"
                  >
                    <colgroup>
                      <col
                        data-index="3"
                      />
                      <col
                        data-index="4"
                      />
                      <col
                        data-index="5"
                      />
                      <col
                        data-index="6"
                      />
                      <col
                        data-index="7"
                      />
                      <col
                        data-index="8"
                      />
                      <col
                        data-index="9"
                      />
                    </colgroup>
                    <thead>
                      <tr
                        class=""
                      >
                        <th
                          class=""
                          data-index="3"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                ID
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="3"
                          />
                        </th>
                        <th
                          class=""
                          data-index="4"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Rendering engine
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="4"
                          />
                        </th>
                        <th
                          class=""
                          data-index="5"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Browser
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="5"
                          />
                        </th>
                        <th
                          class=""
                          data-index="6"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Platform(s)
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="6"
                          />
                        </th>
                        <th
                          class=""
                          data-index="7"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Engine version
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="7"
                          />
                        </th>
                        <th
                          class=""
                          data-index="8"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                CSS grade
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="8"
                          />
                        </th>
                        <th
                          class="cxd-Table-operationCell"
                          data-index="9"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                操作
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="9"
                          />
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        class="cxd-Table-table-tr cxd-Table-tr--odd cxd-Table-tr--1th"
                        data-index="0"
                      >
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            1
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Trident - pbz7l
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Internet Explorer 4.0
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Win 95+
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            4
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            X
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <div
                            class="cxd-OperationField"
                          >
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm"
                              type="button"
                            >
                              <span>
                                详情
                              </span>
                            </button>
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm text-danger"
                              type="button"
                            >
                              <span>
                                删除
                              </span>
                            </button>
                          </div>
                        </td>
                      </tr>
                      <tr
                        class="cxd-Table-table-tr cxd-Table-tr--even cxd-Table-tr--1th"
                        data-index="1"
                      >
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            2
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Trident - tir4m8
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Internet Explorer 5.0
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Win 95+
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            5
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            C
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <div
                            class="cxd-OperationField"
                          >
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm"
                              type="button"
                            >
                              <span>
                                详情
                              </span>
                            </button>
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm text-danger"
                              type="button"
                            >
                              <span>
                                删除
                              </span>
                            </button>
                          </div>
                        </td>
                      </tr>
                      <tr
                        class="cxd-Table-table-tr cxd-Table-tr--odd cxd-Table-tr--1th"
                        data-index="2"
                      >
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            3
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Trident - wcn6f
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Internet Explorer 5.5
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Win 95+
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            5.5
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            A
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <div
                            class="cxd-OperationField"
                          >
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm"
                              type="button"
                            >
                              <span>
                                详情
                              </span>
                            </button>
                            <div
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm is-disabled text-danger"
                              disabled=""
                            >
                              <span>
                                删除
                              </span>
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr
                        class="cxd-Table-table-tr cxd-Table-tr--even cxd-Table-tr--1th"
                        data-index="3"
                      >
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            4
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Trident - uwmcbf
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Internet Explorer 6
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Win 98+
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            6
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            A
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <div
                            class="cxd-OperationField"
                          >
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm"
                              type="button"
                            >
                              <span>
                                详情
                              </span>
                            </button>
                            <div
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm is-disabled text-danger"
                              disabled=""
                            >
                              <span>
                                删除
                              </span>
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr
                        class="cxd-Table-table-tr cxd-Table-tr--odd cxd-Table-tr--1th"
                        data-index="4"
                      >
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            5
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Trident - yjgst7
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Internet Explorer 7
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Win XP SP2+
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            7
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            A
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <div
                            class="cxd-OperationField"
                          >
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm"
                              type="button"
                            >
                              <span>
                                详情
                              </span>
                            </button>
                            <div
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm is-disabled text-danger"
                              disabled=""
                            >
                              <span>
                                删除
                              </span>
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr
                        class="cxd-Table-table-tr cxd-Table-tr--even cxd-Table-tr--1th"
                        data-index="5"
                      >
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            6
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Trident - w9ee2k
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            AOL browser (AOL desktop)
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Win XP
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            6
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            A
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <div
                            class="cxd-OperationField"
                          >
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm"
                              type="button"
                            >
                              <span>
                                详情
                              </span>
                            </button>
                            <div
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm is-disabled text-danger"
                              disabled=""
                            >
                              <span>
                                删除
                              </span>
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr
                        class="cxd-Table-table-tr cxd-Table-tr--odd cxd-Table-tr--1th"
                        data-index="6"
                      >
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            7
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Gecko - hi6cd
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Firefox 1.0
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Win 98+ / OSX.2+
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            1.7
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            A
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <div
                            class="cxd-OperationField"
                          >
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm"
                              type="button"
                            >
                              <span>
                                详情
                              </span>
                            </button>
                            <div
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm is-disabled text-danger"
                              disabled=""
                            >
                              <span>
                                删除
                              </span>
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr
                        class="cxd-Table-table-tr cxd-Table-tr--even cxd-Table-tr--1th"
                        data-index="7"
                      >
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            8
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Gecko - 4kxz6
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Firefox 1.5
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Win 98+ / OSX.2+
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            1.8
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            A
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <div
                            class="cxd-OperationField"
                          >
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm"
                              type="button"
                            >
                              <span>
                                详情
                              </span>
                            </button>
                            <div
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm is-disabled text-danger"
                              disabled=""
                            >
                              <span>
                                删除
                              </span>
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr
                        class="cxd-Table-table-tr cxd-Table-tr--odd cxd-Table-tr--1th"
                        data-index="8"
                      >
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            9
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Gecko - x0u91o
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Firefox 2.0
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Win 98+ / OSX.2+
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            1.8
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            A
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <div
                            class="cxd-OperationField"
                          >
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm"
                              type="button"
                            >
                              <span>
                                详情
                              </span>
                            </button>
                            <div
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm is-disabled text-danger"
                              disabled=""
                            >
                              <span>
                                删除
                              </span>
                            </div>
                          </div>
                        </td>
                      </tr>
                      <tr
                        class="cxd-Table-table-tr cxd-Table-tr--even cxd-Table-tr--1th"
                        data-index="9"
                      >
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            10
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Gecko - iou01
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Firefox 3.0
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            Win 2k+ / OSX.3+
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            1.9
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <span
                            class="cxd-PlainField"
                          >
                            A
                          </span>
                        </td>
                        <td
                          class=""
                        >
                          <div
                            class="cxd-OperationField"
                          >
                            <button
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm"
                              type="button"
                            >
                              <span>
                                详情
                              </span>
                            </button>
                            <div
                              class="cxd-Button cxd-Button--link cxd-Button--size-sm is-disabled text-danger"
                              disabled=""
                            >
                              <span>
                                删除
                              </span>
                            </div>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
              <div
                class="cxd-Table-toolbar cxd-Table-footToolbar"
              >
                <div
                  class="cxd-Crud-toolbar"
                >
                  <div
                    class="cxd-Crud-toolbar-item cxd-Crud-toolbar-item--left"
                  >
                    <div
                      class="cxd-Crud-statistics"
                    >
                      1/18 共：171 项
                    </div>
                  </div>
                  <div
                    class="cxd-Crud-toolbar-item cxd-Crud-toolbar-item--right"
                  >
                    <div
                      class="cxd-Crud-pager"
                    >
                      <div
                        class="cxd-Pagination-wrap cxd-Pagination-wrap-size--md"
                      >
                        <ul
                          class="cxd-Pagination cxd-Pagination--sm cxd-Pagination-item"
                        >
                          <li
                            class="cxd-Pagination-prev is-disabled"
                          >
                            <span>
                              <icon-mock
                                classname="icon icon-left-arrow"
                                icon="left-arrow"
                              />
                            </span>
                          </li>
                          <li
                            class="cxd-Pagination-pager-item is-active"
                          >
                            <a
                              role="button"
                            >
                              1
                            </a>
                          </li>
                          <li
                            class="cxd-Pagination-pager-item"
                          >
                            <a
                              role="button"
                            >
                              2
                            </a>
                          </li>
                          <li
                            class="cxd-Pagination-pager-item"
                          >
                            <a
                              role="button"
                            >
                              3
                            </a>
                          </li>
                          <li
                            class="cxd-Pagination-pager-item"
                          >
                            <a
                              role="button"
                            >
                              4
                            </a>
                          </li>
                          <li
                            class="cxd-Pagination-pager-item"
                          >
                            <a
                              role="button"
                            >
                              5
                            </a>
                          </li>
                          <li
                            class="cxd-Pagination-pager-item"
                          >
                            <a
                              role="button"
                            >
                              6
                            </a>
                          </li>
                          <li
                            class="cxd-Pagination-pager-item"
                          >
                            <a
                              role="button"
                            >
                              7
                            </a>
                          </li>
                          <li
                            class="cxd-Pagination-ellipsis"
                          >
                            <a
                              role="button"
                            >
                              ...
                            </a>
                            <span
                              class="icon"
                            >
                              <icon-mock
                                classname="icon icon-arrow-double-right"
                                icon="arrow-double-right"
                              />
                            </span>
                          </li>
                          <li
                            class="cxd-Pagination-pager-item"
                          >
                            <a
                              role="button"
                            >
                              18
                            </a>
                          </li>
                          <li
                            class="cxd-Pagination-next"
                          >
                            <span>
                              <icon-mock
                                classname="icon icon-right-arrow"
                                icon="right-arrow"
                              />
                            </span>
                          </li>
                        </ul>
                        <div
                          class="cxd-Pagination-inputGroup cxd-Pagination-item"
                        >
                          <span
                            class="cxd-Pagination-inputGroup-left"
                          >
                            跳转至
                          </span>
                          <input
                            class="cxd-Pagination-inputGroup-input"
                            type="text"
                            value=""
                          />
                          <span
                            class="cxd-Pagination-inputGroup-right"
                          >
                            前往
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`doAction:crud reload with data1 1`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--primary cxd-Button--size-default mb-2"
            type="button"
          >
            <span>
              刷新CRUD数据加载请求，同时追加参数date
            </span>
          </button>
          <div
            class="cxd-Crud is-loading"
            data-id="crud_reload"
            data-role="container"
          >
            <div
              class="cxd-Table cxd-Crud-body"
              data-id="crud_reload"
              style="--Table-thead-height: 0px; --Table-column-3-width: 0px; --Table-column-4-width: 0px; --Table-column-5-width: 0px; --Table-column-6-width: 0px; --Table-column-7-width: 0px; --Table-column-8-width: 0px; --Table-column-9-width: 0px;"
            >
              <div
                class="cxd-Table-fixedTop"
              >
                <div
                  class="cxd-Table-toolbar cxd-Table-headToolbar"
                >
                  <div
                    class="cxd-Table-actions"
                  >
                    <div
                      class="cxd-ColumnToggler"
                    >
                      <button
                        class="cxd-Button cxd-Button--default cxd-Button--size-sm"
                      >
                        <icon-mock
                          classname="icon icon-columns"
                          icon="columns"
                        />
                        <span
                          class="cxd-ColumnToggler-caret"
                        >
                          <icon-mock
                            classname="icon icon-right-arrow-bold"
                            icon="right-arrow-bold"
                          />
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="cxd-Table-contentWrap"
              >
                <div
                  class="cxd-Table-content"
                >
                  <table
                    class="cxd-Table-table"
                  >
                    <colgroup>
                      <col
                        data-index="3"
                      />
                      <col
                        data-index="4"
                      />
                      <col
                        data-index="5"
                      />
                      <col
                        data-index="6"
                      />
                      <col
                        data-index="7"
                      />
                      <col
                        data-index="8"
                      />
                      <col
                        data-index="9"
                      />
                    </colgroup>
                    <thead>
                      <tr
                        class=""
                      >
                        <th
                          class=""
                          data-index="3"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                ID
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="3"
                          />
                        </th>
                        <th
                          class=""
                          data-index="4"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Rendering engine
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="4"
                          />
                        </th>
                        <th
                          class=""
                          data-index="5"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Browser
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="5"
                          />
                        </th>
                        <th
                          class=""
                          data-index="6"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Platform(s)
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="6"
                          />
                        </th>
                        <th
                          class=""
                          data-index="7"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Engine version
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="7"
                          />
                        </th>
                        <th
                          class=""
                          data-index="8"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                CSS grade
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="8"
                          />
                        </th>
                        <th
                          class="cxd-Table-operationCell"
                          data-index="9"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                操作
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="9"
                          />
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        class="cxd-Table-placeholder"
                      />
                    </tbody>
                  </table>
                </div>
                <div
                  class="cxd-Spinner-overlay in"
                />
                <div
                  class="cxd-Spinner cxd-Spinner--overlay in"
                  data-testid="spinner"
                >
                  <div
                    class="cxd-Spinner-icon cxd-Spinner-icon--default"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`doAction:crud reload with data2 1`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--primary cxd-Button--size-default mb-2"
            type="button"
          >
            <span>
              刷新CRUD数据加载请求，同时追加按钮所在数据域的所有数据
            </span>
          </button>
          <div
            class="cxd-Crud is-loading"
            data-id="crud_reload"
            data-role="container"
          >
            <div
              class="cxd-Table cxd-Crud-body"
              data-id="crud_reload"
              style="--Table-thead-height: 0px; --Table-column-3-width: 0px; --Table-column-4-width: 0px; --Table-column-5-width: 0px; --Table-column-6-width: 0px; --Table-column-7-width: 0px; --Table-column-8-width: 0px; --Table-column-9-width: 0px;"
            >
              <div
                class="cxd-Table-fixedTop"
              >
                <div
                  class="cxd-Table-toolbar cxd-Table-headToolbar"
                >
                  <div
                    class="cxd-Table-actions"
                  >
                    <div
                      class="cxd-ColumnToggler"
                    >
                      <button
                        class="cxd-Button cxd-Button--default cxd-Button--size-sm"
                      >
                        <icon-mock
                          classname="icon icon-columns"
                          icon="columns"
                        />
                        <span
                          class="cxd-ColumnToggler-caret"
                        >
                          <icon-mock
                            classname="icon icon-right-arrow-bold"
                            icon="right-arrow-bold"
                          />
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="cxd-Table-contentWrap"
              >
                <div
                  class="cxd-Table-content"
                >
                  <table
                    class="cxd-Table-table"
                  >
                    <colgroup>
                      <col
                        data-index="3"
                      />
                      <col
                        data-index="4"
                      />
                      <col
                        data-index="5"
                      />
                      <col
                        data-index="6"
                      />
                      <col
                        data-index="7"
                      />
                      <col
                        data-index="8"
                      />
                      <col
                        data-index="9"
                      />
                    </colgroup>
                    <thead>
                      <tr
                        class=""
                      >
                        <th
                          class=""
                          data-index="3"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                ID
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="3"
                          />
                        </th>
                        <th
                          class=""
                          data-index="4"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Rendering engine
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="4"
                          />
                        </th>
                        <th
                          class=""
                          data-index="5"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Browser
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="5"
                          />
                        </th>
                        <th
                          class=""
                          data-index="6"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Platform(s)
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="6"
                          />
                        </th>
                        <th
                          class=""
                          data-index="7"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                Engine version
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="7"
                          />
                        </th>
                        <th
                          class=""
                          data-index="8"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                CSS grade
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="8"
                          />
                        </th>
                        <th
                          class="cxd-Table-operationCell"
                          data-index="9"
                        >
                          <div
                            class="cxd-TableCell--title"
                          >
                            <span
                              class="cxd-TplField fr-view"
                            >
                              <span>
                                操作
                              </span>
                            </span>
                          </div>
                          <div
                            class="cxd-Table-content-colDragLine"
                            data-index="9"
                          />
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr
                        class="cxd-Table-placeholder"
                      />
                    </tbody>
                  </table>
                </div>
                <div
                  class="cxd-Spinner-overlay in"
                />
                <div
                  class="cxd-Spinner cxd-Spinner--overlay in"
                  data-testid="spinner"
                >
                  <div
                    class="cxd-Spinner-icon cxd-Spinner-icon--default"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
