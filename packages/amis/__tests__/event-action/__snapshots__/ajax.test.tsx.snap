// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`EventAction:ajax 1`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--primary cxd-Button--size-default"
            type="button"
          >
            <span>
              发送请求
            </span>
          </button>
          <span
            class="cxd-TplField fr-view"
          >
            <span>
              18岁的天空
            </span>
          </span>
          <button
            class="cxd-Button cxd-Button--primary cxd-Button--size-default"
            type="button"
          >
            <span>
              发送请求2
            </span>
          </button>
          <span
            class="cxd-TplField fr-view"
          >
            <span>
              18岁的天空，status:0，msg:ok
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`EventAction:ajax args 1`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--primary cxd-Button--size-default"
            type="button"
          >
            <span>
              发送请求
            </span>
          </button>
          <span
            class="cxd-TplField fr-view"
          >
            <span>
              18岁的天空
            </span>
          </span>
          <button
            class="cxd-Button cxd-Button--primary cxd-Button--size-default"
            type="button"
          >
            <span>
              发送请求2
            </span>
          </button>
          <span
            class="cxd-TplField fr-view"
          >
            <span>
              18岁的天空，status:0，msg:ok
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`EventAction:ajax sendOn 1`] = `
<div>
  <div
    class="cxd-Page"
  >
    <div
      class="cxd-Page-content"
    >
      <div
        class="cxd-Page-main"
      >
        <div
          class="cxd-Page-body"
          role="page-body"
        >
          <button
            class="cxd-Button cxd-Button--primary cxd-Button--size-default"
            type="button"
          >
            <span>
              发送请求
            </span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
`;
