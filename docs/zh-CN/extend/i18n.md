---
title: 多语言
---

amis 中对多语言的支持有两方面：

1. amis 内部组件的多语言，比如日期组件中的日期
1. JSON 配置中的多语言，比如配置中的 label 值

## amis 内部组件多语言

分为 JS SDK 和 React 两种用法。

### JS SDK

从 1.1.0 版本开始已经自带英文翻译，所以只需要在 props 里设置 locale 即可。

```javascript
let amisScoped = amis.embed(
  '#root',
  {
    type: 'page',
    title: '表单页面',
    body: {
      type: 'form',
      mode: 'horizontal',
      api: '/saveForm',
      body: [
        {
          label: 'Name',
          type: 'input-text',
          name: 'name'
        }
      ]
    }
  },
  {
    locale: 'en-US'
  }
);
```

如果是其它语言，比如目前德语，需要单独引入文件

```
<script src="sdk.js"></script>
<script src="locale/de-DE.js"></script>
<script type="text/javascript">
  (function () {
    let amis = amisRequire('amis/embed');
    // 通过替换下面这个配置来生成不同页面
    let amisJSON = {
      type: 'page',
      body: {
        type: 'form',
        mode: 'horizontal',
        api: '/saveForm',
        body: [
          {
            label: 'Name',
            type: 'input-text',
            name: 'name'
          },
          {
            label: 'Email',
            type: 'input-email',
            name: 'email'
          }
        ]
      }
    };
    let amisScoped = amis.embed('#root', amisJSON, {
      locale: 'de-DE'
    });
  })();
</script>
```

### React

React 版本中没有内置英文翻译，需要自己 import，使用如下方法：

```javascript
import 'amis-ui/lib/locale/en-US';
```

在渲染 amis 组件的时候设置 locale 为 en-US

```javascript
{
  renderAmis(
    {
      type: 'page',
      title: '简单页面',
      body: '内容'
    },
    {
      locale: 'en-US'
    }
  );
}
```

## 全局文本替换

> 1.5.0 及以上版本

amis 渲染的 env 参数可以配置全局文本替换，能基于它实现多语言替换功能

```javascript
// sdk 中的写法
let amisScoped = amis.embed(
  '#root',
  amisJSON,
  {
    // 这是 props
  },
  {
    replaceText: {
      AMIS_HOST: 'https://baidu.gitee.io/amis'
    }
  }
);
```

比如下面的例子会对 `AMIS_HOST` 进行替换

```schema: scope="body"
{
  "type": "tpl",
  "tpl": "AMIS_HOST"
}
```

这个配置会对配置中的绝大部分字段进行替换，除了 `type, name, mode, target, reload` 这几个有特殊功能的字段。

可以通过配置 `replaceTextIgnoreKeys` 属性来进行修改，让其它字段也避免被替换。

## JSON 配置中设置多语言

在 JSON 配置中，也可以设置不同语言下的不同展现，比如前面设置了 `locale` 为 `en-US`，这时在任意 JSON 配置中都能使用 `en-US` 对象来覆盖这个语言下的效果，用于实现简单的替换效果。

```schema: scope="body"
{
  "type": "form",
  "body": [{
    "type": "input-text",
    "name": "name",
    "label": "姓名：",
    "en-US": {
      "label": "username: "
    }
  }]
}
```

请点击上方的切换语言下拉框切换到英文，就能看到 `label` 属性被替换了，除了 `label` 以外，还可以覆盖其他任意属性，比如将 type 换成其他。

## 扩展内置组件的语言

如果想扩展其他语言，首先参考 `https://github.com/baidu/amis/blob/master/packages/amis-ui/src/locale/en-US.ts` 文件，然后参考后面的示例注册新语言，未翻译的文字都将使用中文。

### JS SDK 扩展方法

```javascript
let amisLib = amisRequire('amis');
amisLib.registerLocale('jp', {
  'Form.submit': '送信'
});
```

### React 扩展方法

```javascript
import {registerLocale} from 'amis';
registerLocale('jp', {
  'Form.submit': '送信'
});
```
