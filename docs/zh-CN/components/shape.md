---
title: Shape 形状
description:
type: 0
group: ⚙ 组件
menuName: Tabs
icon:
---

用于展示形状

## 基本用法

```schema
{
    type: "page",
    body: [
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'triangle'
      },
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'square'
      },
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'pentagon'
      },
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'star'
      }
    ]
}
```

## 配置大小

```schema
{
    type: "page",
    body: [
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'triangle',
        width: 200,
        height: 100
      },
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'square',
        height: 100
      },
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'pentagon',
        width: 100,
        height: 100
      }
    ]
}
```

## 配置圆角

```schema
{
    type: "page",
    body: [
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'triangle',
        radius: 4
      },
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'square',
        radius: -4
      },
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'pentagon',
        radius: 4
      }
    ]
}
```

## 配置边框

```schema
{
    type: "page",
    body: [
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'triangle',
        width: 100,
        height: 100,
        color: '#eee',
        stroke: '#000',
        strokeWidth: 4,
        strokeType: 'line',
      },
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'square',
        width: 100,
        height: 100,
        color: '#eee',
        stroke: '#000',
        strokeWidth: 4,
        strokeType: 'dash',
      },
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'pentagon',
        width: 100,
        height: 100,
        color: '#eee',
        stroke: '#000',
        strokeWidth: 4,
        strokeType: 'dot',
      },
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'star',
        width: 100,
        height: 100,
        color: '#eee',
        stroke: '#000',
        strokeWidth: 4,
        strokeType: 'dot',
      }
    ]
}
```

## 配置颜色

```schema
{
    type: "page",
    body: [
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'triangle',
        color: '#2468f2',
        radius: 4
      },
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'heart',
        color: '#f23d3d'
      }
    ]
}
```

## 自定义路径

```schema
{
    type: "page",
    body: [
      {
        type: 'shape',
        className: 'm-2',
        shapeType: 'custom',
        color: "#037bff",
        paths: [
          "M28.7312529,0.451996665 L1.92575684,15.8327405 C0.641918945,16.9201671 0,17.8552977 0,18.6381323 C0,19.4209669 0,23.7793686 0,31.7133374 L30.1233295,49.1882469 L54.74597,35.1754816 L54.74597,42.2129373 L31.0027368,56.1570351 C30.3803008,56.644996 29.8461111,56.644996 29.4001678,56.1570351 C28.9542245,55.6690741 19.1541686,49.8258134 0,38.6272531 L0,45.7376111 L28.7312529,62.5701394 C29.9104176,63.1432869 31.0961916,63.1432869 32.2885747,62.5701394 C33.4809578,61.9969918 43.0514329,56.3861491 61,45.7376111 L61,24.3532598 C43.0365968,34.8706536 33.4661217,40.4338687 32.2885747,41.0429051 C30.5222542,41.9564597 30.0828355,41.8389106 28.7312529,41.0429051 C27.8301979,40.5122348 20.3630035,36.1320948 6.32966971,27.9024851 L6.32966971,21.8340463 L16.8640877,27.2811313 C18.1811929,27.7179433 19.2031727,27.7179433 19.9300272,27.2811313 C20.6568816,26.8443193 28.2327888,22.3902338 42.6577487,13.9188748 L47.8268453,17.033462 C48.2333133,17.6822864 48.064461,18.2171765 47.3202883,18.6381323 C46.5761157,19.0590881 39.0914758,23.234167 24.8663686,31.1633689 L30.1233295,35.1754816 L61,17.9315096 L43.3323939,6.26763183 L18.1933006,20.3607536 L13.3840291,17.033462 L36.6348401,3.39932681 L32.7397763,0.451996665 C31.8901421,0.150665555 31.2386503,0 30.7853012,0 C30.331952,0 29.6472692,0.150665555 28.7312529,0.451996665 Z",
          "M85.4606259,16 C91.6887073,16 98,19.9446168 98,24.4357411 L98,40.7476187 C96.2218371,46.2492062 93.4906486,49 89.8064346,49 C86.1222206,49 83.1967025,48.9610235 81.0298802,48.8830705 C79.1204375,48.1724054 77.850878,47.5467586 77.2212016,47.0061299 C75.1137392,45.1967008 74.0196778,43.6199029 73.6733281,42.7807824 C73.2701673,41.8040225 71.9205412,38.0832352 74.7691152,33.7965154 C76.4122418,31.3238313 79.7102167,29.6814742 82.6168695,29.3805022 L92.0014918,29.3805022 L92.0014918,24.4357411 C90.1259032,22.5455883 87.9456146,21.600512 85.4606259,21.600512 L84.7605642,21.6006889 C81.8851835,21.6087957 80.8791028,21.7977752 74.4275673,25.5002778 L74.4275673,20.825158 C77.6308853,17.608386 81.3085715,16 85.4606259,16 Z M92.1136435,33.8360358 L84.9688911,33.8360358 C80.9383044,34.662764 78.9230111,36.3792944 78.9230111,38.985627 C78.9230111,41.5919597 80.2819885,43.3084901 82.9999432,44.1352182 L87.9615254,44.1352182 C90.0138334,44.1352182 91.3978727,42.8170486 92.1136435,40.1807095 L92.1136435,33.8360358 Z",
          "M106,45.9058326 L106.12203,26.0591328 C107.998819,19.3539517 112.44853,16.0013611 119.471163,16.0013611 C123.091874,15.9443424 126.348706,17.6787713 129.241661,21.2046476 C131.146086,17.7357899 134.533925,16.0013611 139.405178,16.0013611 C142.929724,16.0013611 147.449054,17.71895 149.676343,21.7722423 C150.188086,22.7035287 150.629305,24.1324922 151,26.0591328 L150.880889,48 L148.277496,48 C146.491015,47.376753 145.444467,46.3780151 145.137852,45.0037864 C145.137852,44.4024223 145.137852,38.5872358 145.137852,27.5582269 C144.726084,23.8837882 142.815193,21.9551266 139.405178,21.7722423 C134.290157,21.4979157 131.885977,24.5600387 131.885977,26.0591328 C131.885977,27.0585289 131.885977,34.3721513 131.885977,48 L127.514392,48 C126.066497,47.117215 125.34255,46.1184772 125.34255,45.0037864 C125.34255,43.8890957 125.34255,37.7903229 125.34255,26.707468 C124.168415,23.4173175 121.973924,21.7722423 118.759078,21.7722423 C113.936808,21.7722423 112.018661,24.9975268 112.018661,26.707468 C112.018661,27.8474288 112.018661,34.9449395 112.018661,48 L108.414781,48 C107.890079,47.9047657 107.35644,47.6282578 106.813864,47.1704763 C106.271288,46.7126949 106,46.291147 106,45.9058326 Z",
          "M160.140201,16.7159953 L160.140201,42.1967437 C160.100175,42.583777 160.383858,43.4742319 160.991248,44.8681084 C161.950865,47.0702956 165.286128,49 168.094575,49 C171.736366,49 173.350406,47.1468859 172.936694,43.4406577 L168.094575,43.4406577 C166.412106,43.1933185 165.570871,42.1819868 165.570871,40.4066624 C165.570871,38.6313381 165.570871,31.8283876 165.570871,19.997811 C164.879352,18.0634863 163.857786,16.9695478 162.506173,16.7159953 C161.15456,16.4624429 160.365903,16.4624429 160.140201,16.7159953 Z M162.757704,12.6808435 C164.833027,12.6808435 166.515408,10.9614281 166.515408,8.84042173 C166.515408,6.71941538 164.833027,5 162.757704,5 C160.682381,5 159,6.71941538 159,8.84042173 C159,10.9614281 160.682381,12.6808435 162.757704,12.6808435 Z",
          "M177.00791,25.6466313 C176.697727,34.2604795 185.59716,34.4875652 188.32068,34.8600665 C191.0442,35.2325677 193.783492,36.1062875 193.783492,39.4670446 C193.783492,42.8278017 191.560993,43.4871964 189.446164,43.4871964 C187.331335,43.4871964 187.047369,43.6717882 184.447219,43.4871964 C182.713785,43.3641352 180.442017,42.1357631 177.631913,39.8020801 L177.631913,44.8030644 C179.513873,47.6010215 182.71894,49 187.247112,49 C194.03937,49 200,45.5737365 200,38.8852924 C200,32.1968484 193.250147,29.9615425 185.661293,28.8150427 C182.33376,27.9726002 182.963706,26.4071448 182.963706,24.4700092 C182.963706,22.5328736 186.910243,21.4851146 189.928058,21.4851146 C192.945874,21.4851146 198.713517,23.9817612 198.713517,24.7718362 C198.713517,25.2985529 198.713517,23.5269195 198.713517,19.4569362 C197.801499,18.3656594 196.499944,17.4867226 194.808852,16.8201258 C192.272215,15.8202307 190.383829,16.0153824 187.522469,16.0153824 C182.366988,16.0153824 177.197843,20.3721739 177.00791,25.6466313 Z"
        ]
      }
    ]
}
```

## 更多图形

```schema
{
  "type": "page",
  "data": {
    "rows": [
      {"name": "正方形", "shapeType": "square"},
      {"name": "等边三角形", "shapeType": "triangle"},
      {"name": "直角三角形", "shapeType": "right-triangle"},
      {"name": "矩形", "shapeType": "rectangle"},
      {"name": "凸弧矩形", "shapeType": "convex-arc-rectangle", "radius": 3},
      {"name": "凹弧矩形", "shapeType": "concave-arc-rectangle", "radius": 3},
      {"name": "双凸弧矩形", "shapeType": "double-convex-arc-rectangle", "radius": 4},
      {"name": "双凹弧矩形", "shapeType": "double-concave-arc-rectangle", "radius": 4},
      {"name": "桶形矩形", "shapeType": "barrel-rectangle", "radius": 4},
      {"name": "菱形", "shapeType": "rhombus"},
      {"name": "平行四边形", "shapeType": "parallelogram"},
      {"name": "矩形-类型1", "shapeType": "rectangle-1"},
      {"name": "矩形-类型2", "shapeType": "rectangle-2"},
      {"name": "矩形-类型3", "shapeType": "rectangle-3"},
      {"name": "心形", "shapeType": "heart"},
      {"name": "正五边形", "shapeType": "pentagon"},
      {"name": "正六边形", "shapeType": "hexagon"},
      {"name": "正八边形", "shapeType": "octagon"},
      {"name": "五角星", "shapeType": "star"},
      {"name": "六角星", "shapeType": "hexagon-star"},
      {"name": "圆", "shapeType": "circle"},
      {"name": "箭头", "shapeType": "arrow"},
    ]
  },
  "body": {
    "type": "table",
    "title": "表格",
    "source": "${rows}",
    "columns": [
      {
        "name": "name",
        "label": "形状名称",
        "width": 50
      },
       {
        "name": "shapeType",
        "label": "IShapeType",
        "width": 50
      },
      {
        "name": "shapeType",
        "label": "形状",
        "type": "shape",
        "shapeType": "${shapeType}",
        "radius": "${radius}",
        "width": 50,
        "height": 50
      }
    ]
  }
}
```

## 事件表

> 2.6.1 及以上版本

当前组件会对外派发以下事件，可以通过`onEvent`来监听这些事件，并通过`actions`来配置执行的动作，详细查看[事件动作](../../docs/concepts/event-action)。

| 事件名称 | 事件参数                     | 说明         |
| -------- | ---------------------------- | ------------ |
| click    | `label: string` 鼠标事件对象 | `点击`时触发 |

### click

鼠标点击。

```schema: scope="body"
 {
  type: 'shape',
  className: 'm-2',
  shapeType: 'triangle',
  "width": 100,
  "height": 100,
  onEvent: {
    click: {
      actions: [
        {
          actionType: 'toast',
          args: {
            msgType: 'info',
            msg: '派发点击事件'
          }
        }
      ]
    }
  }
}
```

## 属性表

| 属性名    | 类型         | 默认值    | 说明                  |
| --------- | ------------ | --------- | --------------------- |
| type      | `string`     | `'shape'` | 指定为图形渲染器      |
| shapeType | `IShapeType` | `'-'`     | 图形类型              |
| className | `string`     |           | 自定义 CSS 样式类名   |
| color     | `string`     |           | 填充颜色              |
| width     | `number`     | `200`     | 图形宽度              |
| height    | `number`     | `200`     | 图形大小              |
| radius    | `number`     | `0`       | 圆角大小,负数表示内弧 |
