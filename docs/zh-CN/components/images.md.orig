---
title: Images 图片集
description:
type: 0
group: ⚙ 组件
menuName: Images 图片集合
icon:
order: 53
---

图片集展示，不支持配置初始化接口初始化数据域，所以需要搭配类似像`Service`、`Form`或`CRUD`
这样的，具有配置接口初始化数据域功能的组件，或者手动进行数据域初始化，然后通过`source`属性，获取数据链中的数据，完成数据展示。

## 基本用法

通过 source 关联上下文数据，或者通过 name 关联。

```schema
{
    "type": "page",
    "data": {
        "imageList": [
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
        ]
    },
    "body": [
        {
            "type": "images",
            "source": "${imageList}"
        },
        {
            "type": "divider"
        },
        {
            "type": "images",
            "name": "imageList"
        }
    ]
}
```

也可以静态展示，即不关联数据固定显示。

```schema
{
    "type": "page",
    "body": {
        "type": "images",
        "options": [
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
        ]
    }
}
```
## 图集排版方式
图集排列方式，通过sortType字段调整布局方式。四部分分别是上右下左方向顺时针排列，最小单位为s，m是s的2倍，l是m的2倍。
`'sm-ss-sss-m'| 'sss-ss-ms-m'| 'sms-ss-sms-m' | 'sm-ss-sss-ss' | 'ms-ss-sss-ss'| 'sss-ss-sm-ss' | 'mss-ss-ssm-ss' | 'sss-ss-mm-ss';`
可以通过改变width和height来调整图片大小。默认宽度是800，高度是450。

### 主辅并排式

`sortType: 'sm-ss-sss-m'`

```schema: scope="body"
{
  "type": "page",
  "body": [
    {
      "type": "images",
      "sortType": "sm-ss-sss-m",
      "width":"200",
      "height":"112.5",
      "thumbMode": "cover",
      "value":[
        {
          "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_520,l_1,f_jpg,q_80",
          "title": "图片描述"
        },
        {
          "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_552,l_1,f_jpg,q_80",
          "title": "图片描述"
        },
        {
          "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          "title": "图片描述"
        },
        {
          "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          "title": "图片描述"
        }
      ]
    },
  ]
}
```

### 上下分层式

`sortType: 'sss-ss-ms-m'`

```schema
{
  "type": "page",
  "body": [
    {
      "type": "images",
      "sortType": "sss-ss-ms-m",
      "thumbMode": "cover",
      "width":"200",
      "height":"112.5",
      "value":[
        {
          "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_520,l_1,f_jpg,q_80",
          "title": "图片描述"
        },
        {
          "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          "title": "图片描述"
        },
        {
          "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          "title": "图片描述"
        },
        {
          "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_520,l_1,f_jpg,q_80",
          "title": "图片描述"
        }
      ]
    },
  ]
}
```

### 侧边栏嵌入式

`sortType: 'sms-ss-sms-m'`

```schema
{
  "type": "page",
  "body": [
    {
      "type": "images",
      "sortType": "sms-ss-sms-m",
      "thumbMode": "cover",
      "width":"200",
      "height":"112.5",
      "value":[
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_520,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_520,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_520,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述"
        }
      ],
    },
  ]
}
```

### 黄金比例分割式

`sortType: 'sm-ss-sss-ss'`

```schema
{
  "type": "page",
  "body": [
    {
      "type": "images",
      "sortType": "sm-ss-sss-ss",
      "thumbMode": "cover",
      "width":"200",
      "height":"112.5",
      "value":[
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_520,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述"
        }
      ]
    },
  ]
}
```

### 动态不对称式

`sortType: 'ms-ss-sss-ss'`

```schema
{
  "type": "page",
  "body": [
    {
      "type": "images",
      "sortType": "ms-ss-sss-ss",
      "thumbMode": "cover",
      "width":"200",
      "height":"112.5",
      "value":[
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_520,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述"
        }
      ]
    },
  ]
}
```

### 焦点引导式

`sortType: 'sss-ss-sm-ss'`

```schema
{
  "type": "page",
  "body": [
    {
      "type": "images",
      "sortType": "sss-ss-sm-ss",
      "thumbMode": "cover",
      "width":"200",
      "height":"112.5",
      "value":[
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_520,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        }
      ]
    },
  ]
}
```

### 模块化组合式

`sortType: 'mss-ss-ssm-ss'`

```schema
{
  "type": "page",
  "body": [
    {
      "type": "images",
      "sortType": "mss-ss-ssm-ss",
      "thumbMode": "cover",
      "width":"200",
      "height":"112.5",
      "value":[
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_520,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_520,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        }
      ]
    },
  ]
}
```

### 阶梯错落式

`sortType: 'sss-ss-mm-ss'`

```schema
{
  "type": "page",
  "body": [
    {
      "type": "images",
      "sortType": "sss-ss-mm-ss",
      "thumbMode": "cover",
      "width":"200",
      "height":"112.5",
      "value":[
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_520,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_520,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ]
    },
  ]
}
```

### 网格式

`sortType: 'even-${number}-${number}'` 其中number可以是任意正整数

```schema
{
  "type": "page",
  "body": [
    {
      "type": "images",
      "sortType": "even-3-3",
      "thumbMode": "cover",
      "width":"180",
      "height":"180",
      "value":[
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ]
    },
  ]
}
```

## 文字展示效果
在图集组件中，通过配置 `hoverMode`字段，来控制鼠标移入图片时，图片蒙层及文字效果。通过 `fontStyle`字段控制文字css样式

### 浮层滑动
`"hoverMode": "hover-slide"`


```schema
{
  "type": "page",
  "body": {
    "type": "container",
    "style":{
        width: "400px",
        height: "260px"
    },
    "body": {
      "type": "images",
      "hoverMode": "hover-slide",
      "sortType": "sm-ss-sss-m",
      "thumbMode": "cover",
      "width":"400",
      "height":"250",
      "value": [
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_270,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_240,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ],
    },
  },
}
```

### 上拉
`"hoverMode": "pull-top"`


```schema
{
  "type": "page",
  "body": {
    "type": "container",
    "style":{
        width: "400px",
        height: "260px"
    },
    "body": {
      "type": "images",
      "hoverMode": "pull-top",
      "sortType": "sm-ss-sss-m",
      "thumbMode": "cover",
      "width":"400",
      "height":"250",
      "value":  [
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_270,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_240,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ],
    },
  },
}
```

### 缩小居中
`"hoverMode": "scale-center"`


```schema
{
  "type": "page",
  "body": {
    "type": "container",
    "style":{
        width: "400px",
        height: "260px"
    },
    "body": {
      "type": "images",
      "hoverMode": "scale-center",
      "sortType": "sm-ss-sss-m",
      "thumbMode": "cover",
      "width":"400",
      "height":"250",
      "value": [
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_270,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_240,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ],
    },
  },
}
```

### 缩小居上
`"hoverMode": "scale-top"`


```schema
{
  "type": "page",
  "body": {
    "type": "container",
    "style":{
        width: "400px",
        height: "260px"
    },
    "body": {
      "type": "images",
      "hoverMode": "scale-top",
      "sortType": "sm-ss-sss-m",
      "thumbMode": "cover",
      "width":"400",
      "height":"250",
      "value":  [
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_270,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_240,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ],
    },
  },
}
```

### 文字效果一
`"hoverMode": "text-style-1"`


```schema
{
  "type": "page",
  "body": {
    "type": "container",
    "style":{
        width: "400px",
        height: "260px"
    },
    "body": {
      "type": "images",
      "hoverMode": "text-style-1",
      "sortType": "sm-ss-sss-m",
      "thumbMode": "cover",
      "width":"400",
      "height":"250",
      "value":  [
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_270,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_240,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ],
    },
  },
}
```

### 文字效果二
`"hoverMode": "text-style-2"`


```schema
{
  "type": "page",
  "body": {
    "type": "container",
    "style":{
        width: "400px",
        height: "260px"
    },
    "body": {
      "type": "images",
      "hoverMode": "text-style-2",
      "sortType": "sm-ss-sss-m",
      "thumbMode": "cover",
      "width":"400",
      "height":"250",
      "value":  [
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_270,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_240,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ],
    },
  },
}
```

### 文字效果三
`"hoverMode": "text-style-3"`


```schema
{
  "type": "page",
  "body": {
    "type": "container",
    "style":{
        width: "400px",
        height: "260px"
    },
    "body": {
      "type": "images",
      "hoverMode": "text-style-3",
      "sortType": "sm-ss-sss-m",
      "thumbMode": "cover",
      "width":"400",
      "height":"250",
      "value":  [
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_270,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_240,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ],
    },
  },
}
```

### 文字效果四
`"hoverMode": "text-style-4"`


```schema
{
  "type": "page",
  "body": {
    "type": "container",
    "style":{
        width: "400px",
        height: "260px"
    },
    "body": {
      "type": "images",
      "hoverMode": "text-style-4",
      "sortType": "sm-ss-sss-m",
      "thumbMode": "cover",
      "width":"400",
      "height":"250",
      "value": [
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_270,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_240,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ],
    },
  },
}
```

### 文字效果五
`"hoverMode": "text-style-5"`

```schema
{
  "type": "page",
  "body": {
    "type": "container",
    "style":{
        width: "400px",
        height: "260px"
    },
    "body": {
      "type": "images",
      "hoverMode": "text-style-5",
      "sortType": "sm-ss-sss-m",
      "thumbMode": "cover",
      "width":"400",
      "height":"250",
      "value": [
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_270,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_240,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ],
    },
  },
}
```

### 文字效果六
`"hoverMode": "text-style-6"`


```schema
{
  "type": "page",
  "body": {
    "type": "container",
    "style":{
        width: "400px",
        height: "260px"
    },
    "body": {
      "type": "images",
      "hoverMode": "text-style-6",
      "sortType": "sm-ss-sss-m",
      "thumbMode": "cover",
      "width":"400",
      "height":"250",
      "value": [
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_270,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_240,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ],
    },
  },
}
```

### 文字效果七
`"hoverMode": "text-style-7"`

```schema
{
  "type": "page",
  "body": {
    "type": "container",
    "style":{
        width: "400px",
        height: "260px"
    },
    "body": {
      "type": "images",
      "hoverMode": "text-style-7",
      "sortType": "sm-ss-sss-m",
      "thumbMode": "cover",
      "width":"400",
      "height":"250",
      "value": [
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,h_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_270,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_250,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
        {
          image: "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_240,l_1,f_jpg,q_80",
          title: "图片描述",
          description: "这是一段描述"
        },
      ],
    },
  },
}
```

## 展示模式

通过配置 `displayMode` 可以设置图片集的展示模式,支持以下两种模式:

- `thumb`: 缩略图模式(默认),将图片以缩略图网格的形式展示
- `full`: 大图模式,以幻灯片形式展示单张大图,可左右滑动切换

在大图模式下,可以通过`fullThumbMode`属性来控制图片的缩放模式:

- `cover`: 保持图片比例,填充整个容器,可能会裁剪部分图片(默认)
- `contain`: 保持图片比例,确保图片完整显示在容器内

```schema
{
    "type": "page",
    "data": {
        "images": [
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "title": "图片1"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg",
                "title": "图片2"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg",
                "title": "图片3"
            }
        ]
    },
    "body": [
        {
            "type": "images",
            "source": "${images}",
            "displayMode": "full",
            "fullThumbMode": "cover"
        }
    ]
}
```

## 值格式

除了支持纯文本数组以外，也支持对象数组，如：

```ts
Array<{
  image: string; // 小图，预览图
  src?: string; // 原图
  title?: string; // 标题
  description?: string; // 描述
  [propName: string]: any; // 还可以有其他数据
}>;
```

### 配置预览图地址

需要设置对象中预览图地址的`key`值为`image`

```schema
{
    "type": "page",
    "data": {
        "images": [
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa1",
                "b": "bbb1"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa2",
                "b": "bbb2"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa3",
                "b": "bbb3"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa4",
                "b": "bbb4"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa5",
                "b": "bbb5"
            }
        ]
    },
    "body": {
        "type": "images",
        "source": "${images}"
    }
}
```

如果`key`值不是`image`，也可以在 **images 组件** 上，通过配置`src`，使用数据映射，来获取图片地址

```schema
{
    "type": "page",
    "data": {
        "images": [
            {
                "img": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa1",
                "b": "bbb1"
            },
            {
                "img": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa2",
                "b": "bbb2"
            },
            {
                "img": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa3",
                "b": "bbb3"
            },
            {
                "img": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa4",
                "b": "bbb4"
            },
            {
                "img": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa5",
                "b": "bbb5"
            }
        ]
    },
    "body": {
        "type": "images",
        "source": "${images}",
        "src": "${img}"
    }
}
```

### 配置原图地址

需要设置对象中原图地址的`key`值为`src`

```schema
{
    "type": "page",
    "data": {
        "images": [
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "a": "aaa1",
                "b": "bbb1"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg",
                "a": "aaa2",
                "b": "bbb2"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg",
                "a": "aaa3",
                "b": "bbb3"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg",
                "a": "aaa4",
                "b": "bbb4"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg",
                "a": "aaa5",
                "b": "bbb5"
            }
        ]
    },
    "body": {
        "type": "images",
        "source": "${images}",
        "enlargeAble": true
    }
}
```

如果原图数据的`key`值不是`src`，也可以在 **images 组件** 上，通过配置`originalSrc`，使用数据映射，来获取原图片地址

```schema
{
    "type": "page",
    "data": {
        "images": [
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "source": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "a": "aaa1",
                "b": "bbb1"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "source": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "a": "aaa2",
                "b": "bbb2"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "source": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "a": "aaa3",
                "b": "bbb3"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "source": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "a": "aaa4",
                "b": "bbb4"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "source": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "a": "aaa5",
                "b": "bbb5"
            }
        ]
    },
    "body": {
        "type": "images",
        "source": "${images}",
        "originalSrc": "${source}",
        "enlargeAble": true
    }
}
```

### 配置标题和说明

设置对象中标题的`key`值为`title`，说明的`key`为`description`或`caption`。

```schema
{
    "type": "page",
    "data": {
        "images": [
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "title": "图片1",
                "description": "图片1的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "title": "图片2",
                "description": "图片2的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "title": "图片3",
                "description": "图片3的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "title": "图片4",
                "description": "图片4的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "title": "图片5",
                "description": "图片5的描述"
            }
        ]
    },
    "body": {
        "type": "images",
        "source": "${images}"
    }
}
```

## 显示比例和缩略图模式

比如这个例子配置成 16:9 的比率展示缩略图，并裁剪部分去掉空白。

```schema
{
    "type": "page",
    "body": {
        "type": "images",
        "thumbRatio": "16:9",
        "thumbMode": "cover",
        "options": [
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
        ]
    }
}
```

## 配置放大预览

在 **images 组件** 上，配置`enlargeAble`，支持放大预览

```schema
{
    "type": "page",
    "data": {
        "images": [
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "title": "图片1",
                "description": "图片1的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "title": "图片2",
                "description": "图片2的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "title": "图片3",
                "description": "图片3的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "title": "图片4",
                "description": "图片4的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "title": "图片5",
                "description": "图片5的描述"
            }
        ]
    },
    "body": {
        "type": "images",
        "source": "${images}",
        "enlargeAble": true
    }
}
```

图片组件配置`"enlargeWithGallary": false`可以关闭放大模式下图片集列表的展示，表格中亦是如此。

```schema: scope="body"
{
    "type": "page",
    "data": {
        "images": [
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "title": "图片1",
                "description": "图片1的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "title": "图片2",
                "description": "图片2的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "title": "图片3",
                "description": "图片3的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "title": "图片4",
                "description": "图片4的描述"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "src": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg",
                "title": "图片5",
                "description": "图片5的描述"
            }
        ]
    },
    "body": {
        "type": "images",
        "source": "${images}",
        "enlargeAble": true,
        "enlargeWithGallary": false
    }
}
```

## 用作 Field 时

当用在 Table 的列配置 Column、List 的内容、Card 卡片的内容和表单的 Static-XXX 中时，可以设置`name`属性，映射同名变量

### Table 中的列类型

```schema: scope="body"
{
    "type": "table",
    "data": {
        "items": [
            {
                "id": "1",
                "images": [
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
                ]
            },
            {
                "id": "2",
                "images": [
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
                ]
            },
            {
                "id": "3",
                "images": [
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
                ]
            }
        ]
    },
    "columns": [
        {
            "name": "id",
            "label": "Id"
        },

        {
            "name": "images",
            "label": "颜色",
            "type": "images",
            "enlargeAble": true
        }
    ]
}
```

Table 中图片组件配置`"enlargeWithGallary": true`可以在放大模式下预览列表中的所有图片集。

```schema: scope="body"
{
    "type": "table",
    "data": {
        "items": [
            {
                "id": "1",
                "images": [
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
                ]
            },
            {
                "id": "2",
                "images": [
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
                ]
            },
            {
                "id": "3",
                "images": [
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
                ]
            }
        ]
    },
    "columns": [
        {
            "name": "id",
            "label": "Id"
        },

        {
            "name": "images",
            "label": "颜色",
            "type": "images",
            "enlargeAble": true,
            "enlargeWithGallary": true
        }
    ]
}
```

Table 中图片组件配置`"enlargeWithGallary": false`可以关闭放大模式下图片集列表的展示。

```schema: scope="body"
{
    "type": "table",
    "data": {
        "items": [
            {
                "id": "1",
                "images": [
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
                ]
            },
            {
                "id": "2",
                "images": [
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
                ]
            },
            {
                "id": "3",
                "images": [
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                    "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
                ]
            }
        ]
    },
    "columns": [
        {
            "name": "id",
            "label": "Id"
        },

        {
            "name": "images",
            "label": "颜色",
            "type": "images",
            "enlargeAble": true,
            "enlargeWithGallary": false
        }
    ]
}
```

List 的内容、Card 卡片的内容配置同上。

### Form 中关联数据静态展示

```schema: scope="body"
{
    "type": "form",
    "data": {
        "images": [
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
            "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80"
        ]
    },
    "body": [
        {
            "type": "static-images",
            "name": "images",
            "label": "图片集"
        }
    ]
}
```

## 工具栏

> 2.2.0 及以上版本

配置`"showToolbar": true`使图片在放大模式下开启图片工具栏。配置`"toolbarActions"`
属性可以自定义工具栏的展示方式，具体配置参考[ImageAction](./image#imageaction)

```schema
{
    "type": "page",
    "data": {
        "images": [
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692722/4f3cb4202335.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa1",
                "b": "bbb1"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395692942/d8e4992057f9.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa2",
                "b": "bbb2"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693148/1314a2a3d3f6.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa3",
                "b": "bbb3"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693379/8f2e79f82be0.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa4",
                "b": "bbb4"
            },
            {
                "image": "https://internal-amis-res.cdn.bcebos.com/images/2020-1/1578395693566/552b175ef11d.jpeg@s_0,w_216,l_1,f_jpg,q_80",
                "a": "aaa5",
                "b": "bbb5"
            }
        ]
    },
    "body": {
        "type": "images",
        "source": "${images}",
        "enlargeAble": true,
        "showToolbar": true
    }
}
```

## 属性表

<<<<<<< HEAD
| 属性名             | 类型                                       | 默认值    | 说明                                                                                                                                    | 版本    |
| ------------------ | ------------------------------------------ | --------- | --------------------------------------------------------------------------------------------------------------------------------------- | ------- |
| type               | `string`                                   | `images`  | 如果在 Table、Card 和 List 中，为`"images"`；在 Form 中用作静态展示，为`"static-images"`                                                |
| className          | `string`                                   |           | 外层 CSS 类名                                                                                                                           |
| defaultImage       | `string`                                   |           | 默认展示图片                                                                                                                            |
| value              | `string`或`Array<string>`或`Array<object>` |           | 图片数组                                                                                                                                |
| source             | `string`                                   |           | 数据源                                                                                                                                  |
| delimiter          | `string`                                   | `,`       | 分隔符，当 value 为字符串时，用该值进行分隔拆分                                                                                         |
| src                | `string`                                   |           | 预览图地址，支持数据映射获取对象中图片变量                                                                                              |
| originalSrc        | `string`                                   |           | 原图地址，支持数据映射获取对象中图片变量                                                                                                |
| enlargeAble        | `boolean`                                  |           | 支持放大预览                                                                                                                            |
| enlargeWithGallary | `string`                                   |           | 默认在放大功能展示图片集的所有图片信息；表格中使用时，设置为`true`将展示所有行的图片信息；设置为`false`将关闭放大模式下图片集列表的展示 |
| thumbMode          | `string`                                   | `contain` | 预览图模式，可选：`'w-full'`, `'h-full'`, `'contain'`, `'cover'`                                                                        |
| thumbRatio         | `string`                                   | `1:1`     | 预览图比例，可选：`'1:1'`, `'4:3'`, `'16:9'`                                                                                            |
| showToolbar        | `boolean`                                  | `false`   | 放大模式下是否展示图片的工具栏                                                                                                          | `2.2.0` |
| toolbarActions     | `ImageAction[]`                            |           | 图片工具栏，支持旋转，缩放，默认操作全部开启                                                                                            | `2.2.0` |
| displayMode        | `'thumb' \| 'full'`                        | `'thumb'` | 展示模式,支持缩略图模式（thumb）和大图模式（full）                                                                                      |
| fullThumbMode      | `'cover' \| 'contain'`                     | `'cover'` | 大图模式下的图片缩放模式                                                                                                                |
=======
| 属性名                | 类型                                                                                                                                                         | 默认值                    | 说明                                                                         | 版本      |
|--------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------|----------------------------------------------------------------------------|---------|
| type               | `string`                                                                                                                                                   | `images`               | 如果在 Table、Card 和 List 中，为`"images"`；在 Form 中用作静态展示，为`"static-images"`      |
| className          | `string`                                                                                                                                                   |                        | 外层 CSS 类名                                                                  |
| defaultImage       | `string`                                                                                                                                                   |                        | 默认展示图片                                                                     |
| value              | `string`或`Array<string>`或`Array<object>`                                                                                                                   |                        | 图片数组                                                                       |
| source             | `string`                                                                                                                                                   |                        | 数据源                                                                        |
| delimiter          | `string`                                                                                                                                                   | `,`                    | 分隔符，当 value 为字符串时，用该值进行分隔拆分                                                |
| src                | `string`                                                                                                                                                   |                        | 预览图地址，支持数据映射获取对象中图片变量                                                      |
| originalSrc        | `string`                                                                                                                                                   |                        | 原图地址，支持数据映射获取对象中图片变量                                                       |
| enlargeAble        | `boolean`                                                                                                                                                  |                        | 支持放大预览                                                                     |
| enlargeWithGallary | `string`                                                                                                                                                   |                        | 默认在放大功能展示图片集的所有图片信息；表格中使用时，设置为`true`将展示所有行的图片信息；设置为`false`将关闭放大模式下图片集列表的展示 |
| thumbMode          | `string`                                                                                                                                                   | `contain`              | 预览图模式，可选：`'w-full'`, `'h-full'`, `'contain'`, `'cover'`                    |
| thumbRatio         | `string`                                                                                                                                                   | `1:1`                  | 预览图比例，可选：`'1:1'`, `'4:3'`, `'16:9'`                                        |
| showToolbar        | `boolean`                                                                                                                                                  | `false`                | 放大模式下是否展示图片的工具栏                                                            | `2.2.0` |
| toolbarActions     | `ImageAction[]`                                                                                                                                            |                        | 图片工具栏，支持旋转，缩放，默认操作全部开启                                                     | `2.2.0` |
| sortType           | `'sm-ss-sss-m'｜ 'sss-ss-ms-m'｜'sms-ss-sms-m'｜'sm-ss-sss-ss'｜ 'ms-ss-sss-ss'｜ 'sss-ss-sm-ss'｜ 'mss-ss-ssm-ss'｜ 'sss-ss-mm-ss'｜ 'even-${number}-${number}'` |                        | 图集排列方式                                                                     |         |
| hoverMode          | `hover-slideh｜pull-top｜scale-center｜scale-top｜text-style-1｜text-style-2｜text-style-3｜text-style-4｜text-style-5｜text-style-6｜text-style-7 `                 |                        | 鼠标悬浮时的展示状态                                                                 |         |
| fontStyle          | `{"fontSize":"string", "color": "string", "fontFamily": "string"...}`                                                                                      | `{"fontSize": "15px"}` | 字体样式                                                                       |         |
>>>>>>> main
