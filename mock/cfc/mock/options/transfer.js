/** Transfer分页接口 */
module.exports = function (req, res) {
  const perPage = Number(req.query.perPage || 10);
  const page = Number(req.query.page || 1);

  res.json({
    status: 0,
    msg: 'ok',
    data: {
      count: data.length,
      page: page,
      items: data.concat().splice((page - 1) * perPage, perPage)
    }
  });
};

const data = [
  {
    "label": "<PERSON>",
    "value": "1"
  },
  {
    "label": "<PERSON>",
    "value": "2"
  },
  {
    "label": "<PERSON>",
    "value": "3"
  },
  {
    "label": "<PERSON>",
    "value": "4"
  },
  {
    "label": "<PERSON>",
    "value": "5"
  },
  {
    "label": "<PERSON>",
    "value": "6"
  },
  {
    "label": "<PERSON>",
    "value": "7"
  },
  {
    "label": "<PERSON>",
    "value": "8"
  },
  {
    "label": "<PERSON>",
    "value": "9"
  },
  {
    "label": "<PERSON>",
    "value": "10"
  },
  {
    "label": "<PERSON>",
    "value": "11"
  },
  {
    "label": "<PERSON>",
    "value": "12"
  },
  {
    "label": "<PERSON>",
    "value": "13"
  },
  {
    "label": "<PERSON>",
    "value": "14"
  },
  {
    "label": "<PERSON>",
    "value": "15"
  },
  {
    "label": "<PERSON> <PERSON>",
    "value": "16"
  },
  {
    "label": "<PERSON> <PERSON>",
    "value": "17"
  },
  {
    "label": "<PERSON> <PERSON>",
    "value": "18"
  },
  {
    "label": "<PERSON> <PERSON>",
    "value": "19"
  },
  {
    "label": "<PERSON> <PERSON>",
    "value": "20"
  },
  {
    "label": "Richard Clark",
    "value": "21"
  },
  {
    "label": "Cynthia Martinez",
    "value": "22"
  },
  {
    "label": "Kimberly Walker",
    "value": "23"
  },
  {
    "label": "Timothy Anderson",
    "value": "24"
  },
  {
    "label": "Betty Lee",
    "value": "25"
  },
  {
    "label": "Jeffrey Allen",
    "value": "26"
  },
  {
    "label": "Karen Martinez",
    "value": "27"
  },
  {
    "label": "Anna Lopez",
    "value": "28"
  },
  {
    "label": "Dorothy Anderson",
    "value": "29"
  },
  {
    "label": "David Perez",
    "value": "30"
  },
  {
    "label": "Dorothy Martin",
    "value": "31"
  },
  {
    "label": "George Johnson",
    "value": "32"
  },
  {
    "label": "Donald Jackson",
    "value": "33"
  },
  {
    "label": "Mary Brown",
    "value": "34"
  },
  {
    "label": "Deborah Martinez",
    "value": "35"
  },
  {
    "label": "Donald Jackson",
    "value": "36"
  },
  {
    "label": "Lisa Robinson",
    "value": "37"
  },
  {
    "label": "Laura Martinez",
    "value": "38"
  },
  {
    "label": "Timothy Taylor",
    "value": "39"
  },
  {
    "label": "Joseph Martinez",
    "value": "40"
  },
  {
    "label": "Karen Wilson",
    "value": "41"
  },
  {
    "label": "Karen Walker",
    "value": "42"
  },
  {
    "label": "William Martinez",
    "value": "43"
  },
  {
    "label": "Linda Brown",
    "value": "44"
  },
  {
    "label": "Elizabeth Brown",
    "value": "45"
  },
  {
    "label": "Anna Moore",
    "value": "46"
  },
  {
    "label": "Robert Martinez",
    "value": "47"
  },
  {
    "label": "Edward Hernandez",
    "value": "48"
  },
  {
    "label": "Elizabeth Hall",
    "value": "49"
  },
  {
    "label": "Linda Jackson",
    "value": "50"
  },
  {
    "label": "Brian Jones",
    "value": "51"
  },
  {
    "label": "Amy Thompson",
    "value": "52"
  },
  {
    "label": "Kimberly Wilson",
    "value": "53"
  },
  {
    "label": "Nancy Garcia",
    "value": "54"
  },
  {
    "label": "Mary Thompson",
    "value": "55"
  }
].map(function (item, index) {
  return Object.assign({}, item, {
    id: index + 1
  });
});
